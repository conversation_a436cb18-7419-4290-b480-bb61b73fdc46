# 脱敏管理系统增强功能

## 概述

本次更新大幅改进了脱敏管理系统的前后端功能，提供了完整的规则管理界面和强大的数据操作能力。

## 🚀 新增功能

### 1. 完整的后端API接口

#### 规则管理
- **创建规则**: `POST /api/sanitization/rules`
- **更新规则**: `PUT /api/sanitization/rules/{id}`
- **删除规则**: `DELETE /api/sanitization/rules/{id}`
- **切换规则状态**: `POST /api/sanitization/rules/{id}/toggle`

#### 批量操作
- **批量操作**: `POST /api/sanitization/rules/batch`
  - 支持批量启用、禁用、删除规则

#### 规则验证
- **验证规则**: `POST /api/sanitization/rules/validate`
  - 支持规则配置验证和测试输入

#### 导入导出
- **导出配置**: `GET /api/sanitization/rules/export`
- **导入配置**: `POST /api/sanitization/rules/import`

### 2. 现代化前端界面

#### 仪表板 (Dashboard)
- 📊 规则统计概览
- 📈 按类型和严重程度分布图表
- 🔍 系统状态监控
- ⚡ 实时数据刷新

#### 规则管理 (Rules Management)
- 📝 完整的规则CRUD操作
- 🔍 高级搜索和筛选
- ✅ 批量选择和操作
- 📤 导入导出功能

#### 规则编辑器 (Rule Editor)
- 🎯 智能表单验证
- 🧪 实时规则测试
- 🎨 类型特定字段显示
- 💡 用户友好的界面设计

### 3. 用户体验改进

#### 响应式设计
- 📱 移动端适配
- 🖥️ 桌面端优化
- 📐 自适应布局

#### 交互优化
- 🎭 加载状态指示
- 🔔 实时通知反馈
- ❓ 操作确认对话框
- 🎯 直观的状态显示

#### 操作便利性
- ⌨️ 键盘快捷键支持
- 🖱️ 右键菜单操作
- 📋 批量操作工具栏
- 🔄 自动刷新机制

## 🛠️ 技术改进

### 后端架构
- 🏗️ 模块化服务设计
- 🔒 完整的错误处理
- 📝 API文档注释
- 🧪 规则验证引擎

### 前端架构
- ⚛️ 组件化设计
- 🎨 TypeScript类型安全
- 🎯 状态管理优化
- 📦 代码分割和懒加载

### 数据处理
- 🔄 实时数据同步
- 💾 配置持久化
- 🔍 高效搜索算法
- 📊 数据可视化

## 📋 使用指南

### 启动服务

#### 后端服务
```bash
cd sanitization-config-service
SERVER_PORT=8082 go run main.go
```

#### 前端服务
```bash
cd sanitization-config-service/frontend
npm install
npm start
```

### 访问界面
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8082

### 主要操作流程

#### 1. 查看仪表板
- 访问首页查看系统概览
- 监控规则分布和状态
- 查看系统健康状况

#### 2. 管理规则
- 点击"Rules"标签页
- 使用搜索和筛选功能
- 创建、编辑、删除规则
- 批量操作多个规则

#### 3. 规则编辑
- 填写基本信息（ID、名称、描述）
- 选择规则类型和严重程度
- 配置匹配条件和掩码值
- 使用测试功能验证规则

#### 4. 导入导出
- 导出当前配置为JSON文件
- 导入外部配置文件
- 备份和恢复配置

## 🔧 配置说明

### 环境变量
- `SERVER_PORT`: 服务器端口 (默认: 8080)
- `SERVER_HOST`: 服务器地址 (默认: 0.0.0.0)
- `RULES_CONFIG_FILE`: 规则配置文件路径
- `AUTH_ENABLED`: 是否启用认证
- `LOG_LEVEL`: 日志级别

### 规则类型
- **FIELD_NAME**: 字段名匹配
- **PATTERN**: 正则表达式匹配
- **CONTENT_TYPE**: 内容类型匹配
- **CUSTOM**: 自定义规则

### 严重程度
- **CRITICAL**: 关键 (红色)
- **HIGH**: 高 (橙色)
- **MEDIUM**: 中等 (黄色)
- **LOW**: 低 (绿色)

## 🎯 最佳实践

### 规则设计
1. 使用描述性的规则ID和名称
2. 设置合适的优先级（数值越小优先级越高）
3. 充分测试正则表达式模式
4. 为不同服务设置适当的包含/排除条件

### 性能优化
1. 定期清理不使用的规则
2. 合理设置规则优先级
3. 使用高效的正则表达式
4. 监控系统资源使用情况

### 安全考虑
1. 定期备份配置文件
2. 限制管理界面访问权限
3. 审计规则变更记录
4. 测试规则对系统性能的影响

## 🐛 故障排除

### 常见问题
1. **端口冲突**: 修改SERVER_PORT环境变量
2. **配置文件错误**: 检查JSON格式和字段完整性
3. **前端连接失败**: 确认后端服务正常运行
4. **规则不生效**: 检查规则优先级和匹配条件

### 日志查看
- 后端日志: 控制台输出
- 前端日志: 浏览器开发者工具
- 配置变更: 查看配置文件时间戳

## 📈 未来规划

- 🔍 规则使用统计和分析
- 🔔 规则变更通知机制
- 🎨 更多可视化图表
- 🔐 细粒度权限控制
- 📱 移动端专用应用
- 🤖 智能规则推荐

## 🤝 贡献指南

欢迎提交问题报告和功能建议！请遵循以下步骤：

1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详情请查看 LICENSE 文件。
