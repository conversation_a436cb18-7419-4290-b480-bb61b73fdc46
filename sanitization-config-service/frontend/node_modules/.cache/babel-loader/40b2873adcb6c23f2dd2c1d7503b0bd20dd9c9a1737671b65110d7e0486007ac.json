{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8082';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor for adding auth headers if needed\napi.interceptors.request.use(config => {\n  // Add auth token if available\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor for handling errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Handle unauthorized access\n    localStorage.removeItem('auth_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport const sanitizationApi = {\n  // Get all sanitization rules\n  getRules: async serviceName => {\n    const headers = serviceName ? {\n      'X-Service-Name': serviceName\n    } : {};\n    const response = await api.get('/api/sanitization/rules', {\n      headers\n    });\n    return response.data;\n  },\n  // Create a new rule\n  createRule: async rule => {\n    const response = await api.post('/api/sanitization/rules', rule);\n    return response.data;\n  },\n  // Update an existing rule\n  updateRule: async (ruleId, rule) => {\n    const response = await api.put(`/api/sanitization/rules/${ruleId}`, rule);\n    return response.data;\n  },\n  // Delete a rule\n  deleteRule: async ruleId => {\n    const response = await api.delete(`/api/sanitization/rules/${ruleId}`);\n    return response.data;\n  },\n  // Toggle a specific rule's enabled status\n  toggleRule: async (ruleId, enabled) => {\n    const response = await api.post(`/api/sanitization/rules/${ruleId}/toggle`, {\n      enabled\n    });\n    return response.data;\n  },\n  // Batch operations on multiple rules\n  batchOperation: async (ruleIds, operation) => {\n    const response = await api.post('/api/sanitization/rules/batch', {\n      ruleIds,\n      operation\n    });\n    return response.data;\n  },\n  // Validate a rule configuration\n  validateRule: async (rule, testInput) => {\n    const response = await api.post('/api/sanitization/rules/validate', {\n      rule,\n      testInput\n    });\n    return response.data;\n  },\n  // Reload rules from configuration file\n  reloadRules: async () => {\n    const response = await api.post('/api/sanitization/rules/reload');\n    return response.data;\n  },\n  // Toggle global sanitization switch\n  toggleGlobalSwitch: async enabled => {\n    const response = await api.post('/api/sanitization/toggle', {\n      enabled\n    });\n    return response.data;\n  },\n  // Get service health\n  getHealth: async () => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n  // Get service metrics\n  getMetrics: async () => {\n    const response = await api.get('/metrics');\n    return response.data;\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "sanitizationApi", "getRules", "serviceName", "get", "data", "createRule", "rule", "post", "updateRule", "ruleId", "put", "deleteRule", "delete", "toggleRule", "enabled", "batchOperation", "ruleIds", "operation", "validateRule", "testInput", "reloadRules", "toggleGlobalSwitch", "getHealth", "getMetrics"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport {\n  SanitizationConfig,\n  SanitizationRule,\n  HealthResponse,\n  MetricsResponse,\n  ApiResponse,\n  BatchOperationResponse,\n  ValidationResponse\n} from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8082';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor for adding auth headers if needed\napi.interceptors.request.use(\n  (config) => {\n    // Add auth token if available\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for handling errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Handle unauthorized access\n      localStorage.removeItem('auth_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport const sanitizationApi = {\n  // Get all sanitization rules\n  getRules: async (serviceName?: string): Promise<SanitizationConfig> => {\n    const headers = serviceName ? { 'X-Service-Name': serviceName } : {};\n    const response = await api.get('/api/sanitization/rules', { headers });\n    return response.data;\n  },\n\n  // Create a new rule\n  createRule: async (rule: SanitizationRule): Promise<ApiResponse<SanitizationRule>> => {\n    const response = await api.post('/api/sanitization/rules', rule);\n    return response.data;\n  },\n\n  // Update an existing rule\n  updateRule: async (ruleId: string, rule: SanitizationRule): Promise<ApiResponse<SanitizationRule>> => {\n    const response = await api.put(`/api/sanitization/rules/${ruleId}`, rule);\n    return response.data;\n  },\n\n  // Delete a rule\n  deleteRule: async (ruleId: string): Promise<ApiResponse<any>> => {\n    const response = await api.delete(`/api/sanitization/rules/${ruleId}`);\n    return response.data;\n  },\n\n  // Toggle a specific rule's enabled status\n  toggleRule: async (ruleId: string, enabled: boolean): Promise<ApiResponse<SanitizationRule>> => {\n    const response = await api.post(`/api/sanitization/rules/${ruleId}/toggle`, { enabled });\n    return response.data;\n  },\n\n  // Batch operations on multiple rules\n  batchOperation: async (ruleIds: string[], operation: 'enable' | 'disable' | 'delete'): Promise<BatchOperationResponse> => {\n    const response = await api.post('/api/sanitization/rules/batch', {\n      ruleIds,\n      operation\n    });\n    return response.data;\n  },\n\n  // Validate a rule configuration\n  validateRule: async (rule: SanitizationRule, testInput?: string): Promise<ValidationResponse> => {\n    const response = await api.post('/api/sanitization/rules/validate', {\n      rule,\n      testInput\n    });\n    return response.data;\n  },\n\n  // Reload rules from configuration file\n  reloadRules: async (): Promise<ApiResponse<any>> => {\n    const response = await api.post('/api/sanitization/rules/reload');\n    return response.data;\n  },\n\n  // Toggle global sanitization switch\n  toggleGlobalSwitch: async (enabled: boolean): Promise<{ enabled: boolean; message: string; timestamp: number }> => {\n    const response = await api.post('/api/sanitization/toggle', { enabled });\n    return response.data;\n  },\n\n  // Get service health\n  getHealth: async (): Promise<HealthResponse> => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n\n  // Get service metrics\n  getMetrics: async (): Promise<MetricsResponse> => {\n    const response = await api.get('/metrics');\n    return response.data;\n  },\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAWzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;IACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,OAAO,MAAMU,eAAe,GAAG;EAC7B;EACAC,QAAQ,EAAE,MAAOC,WAAoB,IAAkC;IACrE,MAAMrB,OAAO,GAAGqB,WAAW,GAAG;MAAE,gBAAgB,EAAEA;IAAY,CAAC,GAAG,CAAC,CAAC;IACpE,MAAMT,QAAQ,GAAG,MAAMhB,GAAG,CAAC0B,GAAG,CAAC,yBAAyB,EAAE;MAAEtB;IAAQ,CAAC,CAAC;IACtE,OAAOY,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAC,UAAU,EAAE,MAAOC,IAAsB,IAA6C;IACpF,MAAMb,QAAQ,GAAG,MAAMhB,GAAG,CAAC8B,IAAI,CAAC,yBAAyB,EAAED,IAAI,CAAC;IAChE,OAAOb,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAI,UAAU,EAAE,MAAAA,CAAOC,MAAc,EAAEH,IAAsB,KAA6C;IACpG,MAAMb,QAAQ,GAAG,MAAMhB,GAAG,CAACiC,GAAG,CAAC,2BAA2BD,MAAM,EAAE,EAAEH,IAAI,CAAC;IACzE,OAAOb,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAO,UAAU,EAAE,MAAOF,MAAc,IAAgC;IAC/D,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,MAAM,CAAC,2BAA2BH,MAAM,EAAE,CAAC;IACtE,OAAOhB,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAS,UAAU,EAAE,MAAAA,CAAOJ,MAAc,EAAEK,OAAgB,KAA6C;IAC9F,MAAMrB,QAAQ,GAAG,MAAMhB,GAAG,CAAC8B,IAAI,CAAC,2BAA2BE,MAAM,SAAS,EAAE;MAAEK;IAAQ,CAAC,CAAC;IACxF,OAAOrB,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAW,cAAc,EAAE,MAAAA,CAAOC,OAAiB,EAAEC,SAA0C,KAAsC;IACxH,MAAMxB,QAAQ,GAAG,MAAMhB,GAAG,CAAC8B,IAAI,CAAC,+BAA+B,EAAE;MAC/DS,OAAO;MACPC;IACF,CAAC,CAAC;IACF,OAAOxB,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAc,YAAY,EAAE,MAAAA,CAAOZ,IAAsB,EAAEa,SAAkB,KAAkC;IAC/F,MAAM1B,QAAQ,GAAG,MAAMhB,GAAG,CAAC8B,IAAI,CAAC,kCAAkC,EAAE;MAClED,IAAI;MACJa;IACF,CAAC,CAAC;IACF,OAAO1B,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAgB,WAAW,EAAE,MAAAA,CAAA,KAAuC;IAClD,MAAM3B,QAAQ,GAAG,MAAMhB,GAAG,CAAC8B,IAAI,CAAC,gCAAgC,CAAC;IACjE,OAAOd,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAiB,kBAAkB,EAAE,MAAOP,OAAgB,IAAwE;IACjH,MAAMrB,QAAQ,GAAG,MAAMhB,GAAG,CAAC8B,IAAI,CAAC,0BAA0B,EAAE;MAAEO;IAAQ,CAAC,CAAC;IACxE,OAAOrB,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAkB,SAAS,EAAE,MAAAA,CAAA,KAAqC;IAC9C,MAAM7B,QAAQ,GAAG,MAAMhB,GAAG,CAAC0B,GAAG,CAAC,SAAS,CAAC;IACzC,OAAOV,QAAQ,CAACW,IAAI;EACtB,CAAC;EAED;EACAmB,UAAU,EAAE,MAAAA,CAAA,KAAsC;IAChD,MAAM9B,QAAQ,GAAG,MAAMhB,GAAG,CAAC0B,GAAG,CAAC,UAAU,CAAC;IAC1C,OAAOV,QAAQ,CAACW,IAAI;EACtB;AACF,CAAC;AAED,eAAe3B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}