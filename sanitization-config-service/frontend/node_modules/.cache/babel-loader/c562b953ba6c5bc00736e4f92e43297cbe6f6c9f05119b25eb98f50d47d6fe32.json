{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RulesList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Search, Plus, Edit, Trash2, Power, PowerOff, Filter, MoreVertical, CheckSquare, Square } from 'lucide-react';\nimport { sanitizationApi } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RulesList = ({\n  config,\n  onRefresh,\n  onEditRule,\n  onCreateRule\n}) => {\n  _s();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedRules, setSelectedRules] = useState(new Set());\n  const [filterType, setFilterType] = useState('all');\n  const [filterSeverity, setFilterSeverity] = useState('all');\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [showFilters, setShowFilters] = useState(false);\n  const [actionMenuOpen, setActionMenuOpen] = useState(null);\n  const filteredRules = (config === null || config === void 0 ? void 0 : config.rules.filter(rule => {\n    const matchesSearch = rule.name.toLowerCase().includes(searchTerm.toLowerCase()) || rule.description.toLowerCase().includes(searchTerm.toLowerCase()) || rule.id.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filterType === 'all' || rule.type === filterType;\n    const matchesSeverity = filterSeverity === 'all' || rule.severity === filterSeverity;\n    const matchesStatus = filterStatus === 'all' || filterStatus === 'enabled' && rule.enabled || filterStatus === 'disabled' && !rule.enabled;\n    return matchesSearch && matchesType && matchesSeverity && matchesStatus;\n  })) || [];\n  const handleSelectRule = ruleId => {\n    const newSelected = new Set(selectedRules);\n    if (newSelected.has(ruleId)) {\n      newSelected.delete(ruleId);\n    } else {\n      newSelected.add(ruleId);\n    }\n    setSelectedRules(newSelected);\n  };\n  const handleSelectAll = () => {\n    if (selectedRules.size === filteredRules.length) {\n      setSelectedRules(new Set());\n    } else {\n      setSelectedRules(new Set(filteredRules.map(rule => rule.id)));\n    }\n  };\n  const handleBatchOperation = async operation => {\n    if (selectedRules.size === 0) {\n      toast.error('Please select rules first');\n      return;\n    }\n    const confirmMessage = `Are you sure you want to ${operation} ${selectedRules.size} rule(s)?`;\n    if (!window.confirm(confirmMessage)) {\n      return;\n    }\n    try {\n      const result = await sanitizationApi.batchOperation(Array.from(selectedRules), operation);\n      toast.success(`${operation} operation completed: ${result.successCount} succeeded, ${result.failedCount} failed`);\n      setSelectedRules(new Set());\n      onRefresh();\n    } catch (error) {\n      toast.error(`Batch ${operation} failed`);\n    }\n  };\n  const handleToggleRule = async (ruleId, enabled) => {\n    try {\n      await sanitizationApi.toggleRule(ruleId, enabled);\n      toast.success(`Rule ${enabled ? 'enabled' : 'disabled'} successfully`);\n      onRefresh();\n    } catch (error) {\n      toast.error(`Failed to ${enabled ? 'enable' : 'disable'} rule`);\n    }\n  };\n  const handleDeleteRule = async ruleId => {\n    if (!window.confirm('Are you sure you want to delete this rule?')) {\n      return;\n    }\n    try {\n      await sanitizationApi.deleteRule(ruleId);\n      toast.success('Rule deleted successfully');\n      onRefresh();\n    } catch (error) {\n      toast.error('Failed to delete rule');\n    }\n  };\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'CRITICAL':\n        return 'bg-red-900/50 text-red-400 border border-red-800';\n      case 'HIGH':\n        return 'bg-orange-900/50 text-orange-400 border border-orange-800';\n      case 'MEDIUM':\n        return 'bg-yellow-900/50 text-yellow-400 border border-yellow-800';\n      case 'LOW':\n        return 'bg-green-900/50 text-green-400 border border-green-800';\n      default:\n        return 'bg-gray-800 text-gray-400 border border-gray-700';\n    }\n  };\n  const getTypeColor = type => {\n    switch (type) {\n      case 'FIELD_NAME':\n        return 'bg-blue-900/50 text-blue-400 border border-blue-800';\n      case 'PATTERN':\n        return 'bg-purple-900/50 text-purple-400 border border-purple-800';\n      case 'CONTENT_TYPE':\n        return 'bg-indigo-900/50 text-indigo-400 border border-indigo-800';\n      default:\n        return 'bg-gray-800 text-gray-400 border border-gray-700';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-white\",\n          children: \"Sanitization Rules\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm mt-1\",\n          children: [filteredRules.length, \" of \", (config === null || config === void 0 ? void 0 : config.rules.length) || 0, \" rules\", selectedRules.size > 0 && ` • ${selectedRules.size} selected`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onCreateRule,\n        className: \"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-4 w-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), \"Create Rule\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            placeholder: \"Search rules...\",\n            className: \"w-full pl-10 pr-4 py-2 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowFilters(!showFilters),\n          className: `inline-flex items-center px-4 py-2 rounded-lg transition-colors ${showFilters ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`,\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), \"Filters\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-900 border border-gray-800 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filterType,\n            onChange: e => setFilterType(e.target.value),\n            className: \"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Types\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"FIELD_NAME\",\n              children: \"Field Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"PATTERN\",\n              children: \"Pattern\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"CONTENT_TYPE\",\n              children: \"Content Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"CUSTOM\",\n              children: \"Custom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"Severity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filterSeverity,\n            onChange: e => setFilterSeverity(e.target.value),\n            className: \"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Severities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"CRITICAL\",\n              children: \"Critical\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"HIGH\",\n              children: \"High\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"MEDIUM\",\n              children: \"Medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"LOW\",\n              children: \"Low\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-300 mb-2\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filterStatus,\n            onChange: e => setFilterStatus(e.target.value),\n            className: \"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"enabled\",\n              children: \"Enabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"disabled\",\n              children: \"Disabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), selectedRules.size > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between p-4 bg-blue-900/20 border border-blue-800 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-blue-400 font-medium\",\n        children: [selectedRules.size, \" rule(s) selected\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleBatchOperation('enable'),\n          className: \"inline-flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(Power, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), \"Enable\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleBatchOperation('disable'),\n          className: \"inline-flex items-center px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(PowerOff, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), \"Disable\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleBatchOperation('delete'),\n          className: \"inline-flex items-center px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"h-3 w-3 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), \"Delete\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: filteredRules.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-16\",\n        children: [/*#__PURE__*/_jsxDEV(Search, {\n          className: \"mx-auto h-16 w-16 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-4 text-lg font-medium text-gray-300\",\n          children: \"No rules found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-gray-500\",\n          children: searchTerm || filterType !== 'all' || filterSeverity !== 'all' || filterStatus !== 'all' ? 'Try adjusting your search or filters' : 'Create your first sanitization rule to get started'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-4 bg-gray-900 border border-gray-800 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSelectAll,\n            className: \"text-gray-400 hover:text-white transition-colors\",\n            children: selectedRules.size === filteredRules.length ? /*#__PURE__*/_jsxDEV(CheckSquare, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Square, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-400 text-sm\",\n            children: [\"Select all \", filteredRules.length, \" rules\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), filteredRules.map(rule => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `bg-gray-900 border rounded-2xl p-6 transition-all duration-200 hover:shadow-lg ${selectedRules.has(rule.id) ? 'border-blue-500 bg-blue-900/10' : 'border-gray-800 hover:border-gray-700'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleSelectRule(rule.id),\n                className: \"mt-1 text-gray-400 hover:text-white transition-colors\",\n                children: selectedRules.has(rule.id) ? /*#__PURE__*/_jsxDEV(CheckSquare, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(Square, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-semibold text-white\",\n                    children: rule.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`,\n                    children: rule.severity\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`,\n                    children: rule.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-300 mb-2\",\n                  children: rule.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 mb-3\",\n                  children: [\"ID: \", rule.id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [rule.fieldNames && rule.fieldNames.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-400\",\n                      children: \"Fields:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-2 text-gray-300\",\n                      children: rule.fieldNames.join(', ')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 27\n                  }, this), rule.pattern && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-400\",\n                      children: \"Pattern:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                      className: \"ml-2 text-xs bg-gray-800 text-blue-400 px-2 py-1 rounded-md border border-gray-700\",\n                      children: rule.pattern\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-400\",\n                      children: \"Mask:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n                      className: \"ml-2 text-xs bg-gray-800 text-green-400 px-2 py-1 rounded-md border border-gray-700\",\n                      children: rule.maskValue\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: rule.enabled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-green-400\",\n                    children: \"Enabled\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-gray-600 rounded-full mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Disabled\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActionMenuOpen(actionMenuOpen === rule.id ? null : rule.id),\n                  className: \"p-2 hover:bg-gray-800 rounded-full transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(MoreVertical, {\n                    className: \"h-4 w-4 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 23\n                }, this), actionMenuOpen === rule.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      onEditRule(rule);\n                      setActionMenuOpen(null);\n                    },\n                    className: \"w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-700 flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Edit, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      handleToggleRule(rule.id, !rule.enabled);\n                      setActionMenuOpen(null);\n                    },\n                    className: \"w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-700 flex items-center space-x-2\",\n                    children: [rule.enabled ? /*#__PURE__*/_jsxDEV(PowerOff, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 45\n                    }, this) : /*#__PURE__*/_jsxDEV(Power, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 80\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: rule.enabled ? 'Disable' : 'Enable'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      handleDeleteRule(rule.id);\n                      setActionMenuOpen(null);\n                    },\n                    className: \"w-full px-4 py-2 text-left text-red-400 hover:bg-gray-700 flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 17\n          }, this)\n        }, rule.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n};\n_s(RulesList, \"pVtyEzFbWDatZ62eb2RPeLjIgik=\");\n_c = RulesList;\nexport default RulesList;\nvar _c;\n$RefreshReg$(_c, \"RulesList\");", "map": {"version": 3, "names": ["React", "useState", "Search", "Plus", "Edit", "Trash2", "Power", "PowerOff", "Filter", "MoreVertical", "CheckSquare", "Square", "sanitizationApi", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RulesList", "config", "onRefresh", "onEditRule", "onCreateRule", "_s", "searchTerm", "setSearchTerm", "selectedRules", "setSelectedRules", "Set", "filterType", "setFilterType", "filterSeverity", "setFilterSeverity", "filterStatus", "setFilterStatus", "showFilters", "setShowFilters", "actionMenuOpen", "setActionMenuOpen", "filteredRules", "rules", "filter", "rule", "matchesSearch", "name", "toLowerCase", "includes", "description", "id", "matchesType", "type", "matchesSeverity", "severity", "matchesStatus", "enabled", "handleSelectRule", "ruleId", "newSelected", "has", "delete", "add", "handleSelectAll", "size", "length", "map", "handleBatchOperation", "operation", "error", "confirmMessage", "window", "confirm", "result", "batchOperation", "Array", "from", "success", "successCount", "failedCount", "handleToggleRule", "toggleRule", "handleDeleteRule", "deleteRule", "getSeverityColor", "getTypeColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "onChange", "e", "target", "placeholder", "fieldNames", "join", "pattern", "maskValue", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RulesList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Search, \n  Plus, \n  Edit, \n  Trash2, \n  Power, \n  PowerOff, \n  Filter,\n  MoreVertical,\n  CheckSquare,\n  Square\n} from 'lucide-react';\nimport { SanitizationRule, SanitizationConfig } from '../types';\nimport { sanitizationApi } from '../services/api';\nimport toast from 'react-hot-toast';\n\ninterface RulesListProps {\n  config: SanitizationConfig | null;\n  onRefresh: () => void;\n  onEditRule: (rule: SanitizationRule) => void;\n  onCreateRule: () => void;\n}\n\nconst RulesList: React.FC<RulesListProps> = ({ config, onRefresh, onEditRule, onCreateRule }) => {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedRules, setSelectedRules] = useState<Set<string>>(new Set());\n  const [filterType, setFilterType] = useState<string>('all');\n  const [filterSeverity, setFilterSeverity] = useState<string>('all');\n  const [filterStatus, setFilterStatus] = useState<string>('all');\n  const [showFilters, setShowFilters] = useState(false);\n  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);\n\n  const filteredRules = config?.rules.filter(rule => {\n    const matchesSearch = \n      rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      rule.id.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesType = filterType === 'all' || rule.type === filterType;\n    const matchesSeverity = filterSeverity === 'all' || rule.severity === filterSeverity;\n    const matchesStatus = \n      filterStatus === 'all' || \n      (filterStatus === 'enabled' && rule.enabled) ||\n      (filterStatus === 'disabled' && !rule.enabled);\n\n    return matchesSearch && matchesType && matchesSeverity && matchesStatus;\n  }) || [];\n\n  const handleSelectRule = (ruleId: string) => {\n    const newSelected = new Set(selectedRules);\n    if (newSelected.has(ruleId)) {\n      newSelected.delete(ruleId);\n    } else {\n      newSelected.add(ruleId);\n    }\n    setSelectedRules(newSelected);\n  };\n\n  const handleSelectAll = () => {\n    if (selectedRules.size === filteredRules.length) {\n      setSelectedRules(new Set());\n    } else {\n      setSelectedRules(new Set(filteredRules.map(rule => rule.id)));\n    }\n  };\n\n  const handleBatchOperation = async (operation: 'enable' | 'disable' | 'delete') => {\n    if (selectedRules.size === 0) {\n      toast.error('Please select rules first');\n      return;\n    }\n\n    const confirmMessage = `Are you sure you want to ${operation} ${selectedRules.size} rule(s)?`;\n    if (!window.confirm(confirmMessage)) {\n      return;\n    }\n\n    try {\n      const result = await sanitizationApi.batchOperation(Array.from(selectedRules), operation);\n      toast.success(`${operation} operation completed: ${result.successCount} succeeded, ${result.failedCount} failed`);\n      setSelectedRules(new Set());\n      onRefresh();\n    } catch (error) {\n      toast.error(`Batch ${operation} failed`);\n    }\n  };\n\n  const handleToggleRule = async (ruleId: string, enabled: boolean) => {\n    try {\n      await sanitizationApi.toggleRule(ruleId, enabled);\n      toast.success(`Rule ${enabled ? 'enabled' : 'disabled'} successfully`);\n      onRefresh();\n    } catch (error) {\n      toast.error(`Failed to ${enabled ? 'enable' : 'disable'} rule`);\n    }\n  };\n\n  const handleDeleteRule = async (ruleId: string) => {\n    if (!window.confirm('Are you sure you want to delete this rule?')) {\n      return;\n    }\n\n    try {\n      await sanitizationApi.deleteRule(ruleId);\n      toast.success('Rule deleted successfully');\n      onRefresh();\n    } catch (error) {\n      toast.error('Failed to delete rule');\n    }\n  };\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'CRITICAL': return 'bg-red-900/50 text-red-400 border border-red-800';\n      case 'HIGH': return 'bg-orange-900/50 text-orange-400 border border-orange-800';\n      case 'MEDIUM': return 'bg-yellow-900/50 text-yellow-400 border border-yellow-800';\n      case 'LOW': return 'bg-green-900/50 text-green-400 border border-green-800';\n      default: return 'bg-gray-800 text-gray-400 border border-gray-700';\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'FIELD_NAME': return 'bg-blue-900/50 text-blue-400 border border-blue-800';\n      case 'PATTERN': return 'bg-purple-900/50 text-purple-400 border border-purple-800';\n      case 'CONTENT_TYPE': return 'bg-indigo-900/50 text-indigo-400 border border-indigo-800';\n      default: return 'bg-gray-800 text-gray-400 border border-gray-700';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-xl font-bold text-white\">Sanitization Rules</h2>\n          <p className=\"text-gray-400 text-sm mt-1\">\n            {filteredRules.length} of {config?.rules.length || 0} rules\n            {selectedRules.size > 0 && ` • ${selectedRules.size} selected`}\n          </p>\n        </div>\n        <button\n          onClick={onCreateRule}\n          className=\"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Create Rule\n        </button>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500\" />\n            <input\n              type=\"text\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"Search rules...\"\n              className=\"w-full pl-10 pr-4 py-2 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${\n              showFilters ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'\n            }`}\n          >\n            <Filter className=\"h-4 w-4 mr-2\" />\n            Filters\n          </button>\n        </div>\n\n        {showFilters && (\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-900 border border-gray-800 rounded-lg\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">Type</label>\n              <select\n                value={filterType}\n                onChange={(e) => setFilterType(e.target.value)}\n                className=\"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"all\">All Types</option>\n                <option value=\"FIELD_NAME\">Field Name</option>\n                <option value=\"PATTERN\">Pattern</option>\n                <option value=\"CONTENT_TYPE\">Content Type</option>\n                <option value=\"CUSTOM\">Custom</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">Severity</label>\n              <select\n                value={filterSeverity}\n                onChange={(e) => setFilterSeverity(e.target.value)}\n                className=\"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"all\">All Severities</option>\n                <option value=\"CRITICAL\">Critical</option>\n                <option value=\"HIGH\">High</option>\n                <option value=\"MEDIUM\">Medium</option>\n                <option value=\"LOW\">Low</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">Status</label>\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value)}\n                className=\"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"all\">All Status</option>\n                <option value=\"enabled\">Enabled</option>\n                <option value=\"disabled\">Disabled</option>\n              </select>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Batch Actions */}\n      {selectedRules.size > 0 && (\n        <div className=\"flex items-center justify-between p-4 bg-blue-900/20 border border-blue-800 rounded-lg\">\n          <span className=\"text-blue-400 font-medium\">\n            {selectedRules.size} rule(s) selected\n          </span>\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => handleBatchOperation('enable')}\n              className=\"inline-flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors\"\n            >\n              <Power className=\"h-3 w-3 mr-1\" />\n              Enable\n            </button>\n            <button\n              onClick={() => handleBatchOperation('disable')}\n              className=\"inline-flex items-center px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm transition-colors\"\n            >\n              <PowerOff className=\"h-3 w-3 mr-1\" />\n              Disable\n            </button>\n            <button\n              onClick={() => handleBatchOperation('delete')}\n              className=\"inline-flex items-center px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors\"\n            >\n              <Trash2 className=\"h-3 w-3 mr-1\" />\n              Delete\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Rules List */}\n      <div className=\"space-y-4\">\n        {filteredRules.length === 0 ? (\n          <div className=\"text-center py-16\">\n            <Search className=\"mx-auto h-16 w-16 text-gray-600\" />\n            <h3 className=\"mt-4 text-lg font-medium text-gray-300\">No rules found</h3>\n            <p className=\"mt-2 text-sm text-gray-500\">\n              {searchTerm || filterType !== 'all' || filterSeverity !== 'all' || filterStatus !== 'all'\n                ? 'Try adjusting your search or filters'\n                : 'Create your first sanitization rule to get started'\n              }\n            </p>\n          </div>\n        ) : (\n          <>\n            {/* Select All */}\n            <div className=\"flex items-center space-x-3 p-4 bg-gray-900 border border-gray-800 rounded-lg\">\n              <button\n                onClick={handleSelectAll}\n                className=\"text-gray-400 hover:text-white transition-colors\"\n              >\n                {selectedRules.size === filteredRules.length ? (\n                  <CheckSquare className=\"h-5 w-5\" />\n                ) : (\n                  <Square className=\"h-5 w-5\" />\n                )}\n              </button>\n              <span className=\"text-gray-400 text-sm\">\n                Select all {filteredRules.length} rules\n              </span>\n            </div>\n\n            {/* Rules */}\n            {filteredRules.map((rule) => (\n              <div\n                key={rule.id}\n                className={`bg-gray-900 border rounded-2xl p-6 transition-all duration-200 hover:shadow-lg ${\n                  selectedRules.has(rule.id) \n                    ? 'border-blue-500 bg-blue-900/10' \n                    : 'border-gray-800 hover:border-gray-700'\n                }`}\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-start space-x-4 flex-1\">\n                    <button\n                      onClick={() => handleSelectRule(rule.id)}\n                      className=\"mt-1 text-gray-400 hover:text-white transition-colors\"\n                    >\n                      {selectedRules.has(rule.id) ? (\n                        <CheckSquare className=\"h-5 w-5\" />\n                      ) : (\n                        <Square className=\"h-5 w-5\" />\n                      )}\n                    </button>\n                    \n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-3 mb-2\">\n                        <h4 className=\"text-lg font-semibold text-white\">{rule.name}</h4>\n                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>\n                          {rule.severity}\n                        </span>\n                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>\n                          {rule.type}\n                        </span>\n                      </div>\n                      <p className=\"text-sm text-gray-300 mb-2\">{rule.description}</p>\n                      <div className=\"text-xs text-gray-500 mb-3\">ID: {rule.id}</div>\n                      \n                      <div className=\"space-y-2\">\n                        {rule.fieldNames && rule.fieldNames.length > 0 && (\n                          <div className=\"text-sm\">\n                            <span className=\"font-medium text-gray-400\">Fields:</span>\n                            <span className=\"ml-2 text-gray-300\">{rule.fieldNames.join(', ')}</span>\n                          </div>\n                        )}\n                        {rule.pattern && (\n                          <div className=\"text-sm\">\n                            <span className=\"font-medium text-gray-400\">Pattern:</span>\n                            <code className=\"ml-2 text-xs bg-gray-800 text-blue-400 px-2 py-1 rounded-md border border-gray-700\">{rule.pattern}</code>\n                          </div>\n                        )}\n                        <div className=\"text-sm\">\n                          <span className=\"font-medium text-gray-400\">Mask:</span>\n                          <code className=\"ml-2 text-xs bg-gray-800 text-green-400 px-2 py-1 rounded-md border border-gray-700\">{rule.maskValue}</code>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center\">\n                      {rule.enabled ? (\n                        <>\n                          <div className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></div>\n                          <span className=\"text-sm font-medium text-green-400\">Enabled</span>\n                        </>\n                      ) : (\n                        <>\n                          <div className=\"w-2 h-2 bg-gray-600 rounded-full mr-2\"></div>\n                          <span className=\"text-sm font-medium text-gray-500\">Disabled</span>\n                        </>\n                      )}\n                    </div>\n                    \n                    <div className=\"relative\">\n                      <button\n                        onClick={() => setActionMenuOpen(actionMenuOpen === rule.id ? null : rule.id)}\n                        className=\"p-2 hover:bg-gray-800 rounded-full transition-colors\"\n                      >\n                        <MoreVertical className=\"h-4 w-4 text-gray-400\" />\n                      </button>\n                      \n                      {actionMenuOpen === rule.id && (\n                        <div className=\"absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10\">\n                          <button\n                            onClick={() => {\n                              onEditRule(rule);\n                              setActionMenuOpen(null);\n                            }}\n                            className=\"w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-700 flex items-center space-x-2\"\n                          >\n                            <Edit className=\"h-4 w-4\" />\n                            <span>Edit</span>\n                          </button>\n                          <button\n                            onClick={() => {\n                              handleToggleRule(rule.id, !rule.enabled);\n                              setActionMenuOpen(null);\n                            }}\n                            className=\"w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-700 flex items-center space-x-2\"\n                          >\n                            {rule.enabled ? <PowerOff className=\"h-4 w-4\" /> : <Power className=\"h-4 w-4\" />}\n                            <span>{rule.enabled ? 'Disable' : 'Enable'}</span>\n                          </button>\n                          <button\n                            onClick={() => {\n                              handleDeleteRule(rule.id);\n                              setActionMenuOpen(null);\n                            }}\n                            className=\"w-full px-4 py-2 text-left text-red-400 hover:bg-gray-700 flex items-center space-x-2\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                            <span>Delete</span>\n                          </button>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default RulesList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SACEC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,YAAY,EACZC,WAAW,EACXC,MAAM,QACD,cAAc;AAErB,SAASC,eAAe,QAAQ,iBAAiB;AACjD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASpC,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC/F,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAc,IAAI2B,GAAG,CAAC,CAAC,CAAC;EAC1E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAS,KAAK,CAAC;EAC3D,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAS,KAAK,CAAC;EACnE,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAS,KAAK,CAAC;EAC/D,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAgB,IAAI,CAAC;EAEzE,MAAMsC,aAAa,GAAG,CAAApB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqB,KAAK,CAACC,MAAM,CAACC,IAAI,IAAI;IACjD,MAAMC,aAAa,GACjBD,IAAI,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtB,UAAU,CAACqB,WAAW,CAAC,CAAC,CAAC,IAC1DH,IAAI,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtB,UAAU,CAACqB,WAAW,CAAC,CAAC,CAAC,IACjEH,IAAI,CAACM,EAAE,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtB,UAAU,CAACqB,WAAW,CAAC,CAAC,CAAC;IAE1D,MAAMI,WAAW,GAAGpB,UAAU,KAAK,KAAK,IAAIa,IAAI,CAACQ,IAAI,KAAKrB,UAAU;IACpE,MAAMsB,eAAe,GAAGpB,cAAc,KAAK,KAAK,IAAIW,IAAI,CAACU,QAAQ,KAAKrB,cAAc;IACpF,MAAMsB,aAAa,GACjBpB,YAAY,KAAK,KAAK,IACrBA,YAAY,KAAK,SAAS,IAAIS,IAAI,CAACY,OAAQ,IAC3CrB,YAAY,KAAK,UAAU,IAAI,CAACS,IAAI,CAACY,OAAQ;IAEhD,OAAOX,aAAa,IAAIM,WAAW,IAAIE,eAAe,IAAIE,aAAa;EACzE,CAAC,CAAC,KAAI,EAAE;EAER,MAAME,gBAAgB,GAAIC,MAAc,IAAK;IAC3C,MAAMC,WAAW,GAAG,IAAI7B,GAAG,CAACF,aAAa,CAAC;IAC1C,IAAI+B,WAAW,CAACC,GAAG,CAACF,MAAM,CAAC,EAAE;MAC3BC,WAAW,CAACE,MAAM,CAACH,MAAM,CAAC;IAC5B,CAAC,MAAM;MACLC,WAAW,CAACG,GAAG,CAACJ,MAAM,CAAC;IACzB;IACA7B,gBAAgB,CAAC8B,WAAW,CAAC;EAC/B,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAInC,aAAa,CAACoC,IAAI,KAAKvB,aAAa,CAACwB,MAAM,EAAE;MAC/CpC,gBAAgB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLD,gBAAgB,CAAC,IAAIC,GAAG,CAACW,aAAa,CAACyB,GAAG,CAACtB,IAAI,IAAIA,IAAI,CAACM,EAAE,CAAC,CAAC,CAAC;IAC/D;EACF,CAAC;EAED,MAAMiB,oBAAoB,GAAG,MAAOC,SAA0C,IAAK;IACjF,IAAIxC,aAAa,CAACoC,IAAI,KAAK,CAAC,EAAE;MAC5BjD,KAAK,CAACsD,KAAK,CAAC,2BAA2B,CAAC;MACxC;IACF;IAEA,MAAMC,cAAc,GAAG,4BAA4BF,SAAS,IAAIxC,aAAa,CAACoC,IAAI,WAAW;IAC7F,IAAI,CAACO,MAAM,CAACC,OAAO,CAACF,cAAc,CAAC,EAAE;MACnC;IACF;IAEA,IAAI;MACF,MAAMG,MAAM,GAAG,MAAM3D,eAAe,CAAC4D,cAAc,CAACC,KAAK,CAACC,IAAI,CAAChD,aAAa,CAAC,EAAEwC,SAAS,CAAC;MACzFrD,KAAK,CAAC8D,OAAO,CAAC,GAAGT,SAAS,yBAAyBK,MAAM,CAACK,YAAY,eAAeL,MAAM,CAACM,WAAW,SAAS,CAAC;MACjHlD,gBAAgB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;MAC3BR,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdtD,KAAK,CAACsD,KAAK,CAAC,SAASD,SAAS,SAAS,CAAC;IAC1C;EACF,CAAC;EAED,MAAMY,gBAAgB,GAAG,MAAAA,CAAOtB,MAAc,EAAEF,OAAgB,KAAK;IACnE,IAAI;MACF,MAAM1C,eAAe,CAACmE,UAAU,CAACvB,MAAM,EAAEF,OAAO,CAAC;MACjDzC,KAAK,CAAC8D,OAAO,CAAC,QAAQrB,OAAO,GAAG,SAAS,GAAG,UAAU,eAAe,CAAC;MACtElC,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdtD,KAAK,CAACsD,KAAK,CAAC,aAAab,OAAO,GAAG,QAAQ,GAAG,SAAS,OAAO,CAAC;IACjE;EACF,CAAC;EAED,MAAM0B,gBAAgB,GAAG,MAAOxB,MAAc,IAAK;IACjD,IAAI,CAACa,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MACjE;IACF;IAEA,IAAI;MACF,MAAM1D,eAAe,CAACqE,UAAU,CAACzB,MAAM,CAAC;MACxC3C,KAAK,CAAC8D,OAAO,CAAC,2BAA2B,CAAC;MAC1CvD,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdtD,KAAK,CAACsD,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAMe,gBAAgB,GAAI9B,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,UAAU;QAAE,OAAO,kDAAkD;MAC1E,KAAK,MAAM;QAAE,OAAO,2DAA2D;MAC/E,KAAK,QAAQ;QAAE,OAAO,2DAA2D;MACjF,KAAK,KAAK;QAAE,OAAO,wDAAwD;MAC3E;QAAS,OAAO,kDAAkD;IACpE;EACF,CAAC;EAED,MAAM+B,YAAY,GAAIjC,IAAY,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,YAAY;QAAE,OAAO,qDAAqD;MAC/E,KAAK,SAAS;QAAE,OAAO,2DAA2D;MAClF,KAAK,cAAc;QAAE,OAAO,2DAA2D;MACvF;QAAS,OAAO,kDAAkD;IACpE;EACF,CAAC;EAED,oBACEnC,OAAA;IAAKqE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBtE,OAAA;MAAKqE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDtE,OAAA;QAAAsE,QAAA,gBACEtE,OAAA;UAAIqE,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpE1E,OAAA;UAAGqE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GACtC9C,aAAa,CAACwB,MAAM,EAAC,MAAI,EAAC,CAAA5C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqB,KAAK,CAACuB,MAAM,KAAI,CAAC,EAAC,QACrD,EAACrC,aAAa,CAACoC,IAAI,GAAG,CAAC,IAAI,MAAMpC,aAAa,CAACoC,IAAI,WAAW;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN1E,OAAA;QACE2E,OAAO,EAAEpE,YAAa;QACtB8D,SAAS,EAAC,0GAA0G;QAAAC,QAAA,gBAEpHtE,OAAA,CAACZ,IAAI;UAACiF,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN1E,OAAA;MAAKqE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBtE,OAAA;QAAKqE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CtE,OAAA;UAAKqE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BtE,OAAA,CAACb,MAAM;YAACkF,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/F1E,OAAA;YACEmC,IAAI,EAAC,MAAM;YACXyC,KAAK,EAAEnE,UAAW;YAClBoE,QAAQ,EAAGC,CAAC,IAAKpE,aAAa,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CI,WAAW,EAAC,iBAAiB;YAC7BX,SAAS,EAAC;UAAgK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3K,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1E,OAAA;UACE2E,OAAO,EAAEA,CAAA,KAAMtD,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5CiD,SAAS,EAAE,mEACTjD,WAAW,GAAG,wBAAwB,GAAG,6CAA6C,EACrF;UAAAkD,QAAA,gBAEHtE,OAAA,CAACP,MAAM;YAAC4E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELtD,WAAW,iBACVpB,OAAA;QAAKqE,SAAS,EAAC,yFAAyF;QAAAC,QAAA,gBACtGtE,OAAA;UAAAsE,QAAA,gBACEtE,OAAA;YAAOqE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5E1E,OAAA;YACE4E,KAAK,EAAE9D,UAAW;YAClB+D,QAAQ,EAAGC,CAAC,IAAK/D,aAAa,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CP,SAAS,EAAC,4GAA4G;YAAAC,QAAA,gBAEtHtE,OAAA;cAAQ4E,KAAK,EAAC,KAAK;cAAAN,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC1E,OAAA;cAAQ4E,KAAK,EAAC,YAAY;cAAAN,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C1E,OAAA;cAAQ4E,KAAK,EAAC,SAAS;cAAAN,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC1E,OAAA;cAAQ4E,KAAK,EAAC,cAAc;cAAAN,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClD1E,OAAA;cAAQ4E,KAAK,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1E,OAAA;UAAAsE,QAAA,gBACEtE,OAAA;YAAOqE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChF1E,OAAA;YACE4E,KAAK,EAAE5D,cAAe;YACtB6D,QAAQ,EAAGC,CAAC,IAAK7D,iBAAiB,CAAC6D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACnDP,SAAS,EAAC,4GAA4G;YAAAC,QAAA,gBAEtHtE,OAAA;cAAQ4E,KAAK,EAAC,KAAK;cAAAN,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3C1E,OAAA;cAAQ4E,KAAK,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C1E,OAAA;cAAQ4E,KAAK,EAAC,MAAM;cAAAN,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC1E,OAAA;cAAQ4E,KAAK,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC1E,OAAA;cAAQ4E,KAAK,EAAC,KAAK;cAAAN,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1E,OAAA;UAAAsE,QAAA,gBACEtE,OAAA;YAAOqE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9E1E,OAAA;YACE4E,KAAK,EAAE1D,YAAa;YACpB2D,QAAQ,EAAGC,CAAC,IAAK3D,eAAe,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACjDP,SAAS,EAAC,4GAA4G;YAAAC,QAAA,gBAEtHtE,OAAA;cAAQ4E,KAAK,EAAC,KAAK;cAAAN,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC1E,OAAA;cAAQ4E,KAAK,EAAC,SAAS;cAAAN,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC1E,OAAA;cAAQ4E,KAAK,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL/D,aAAa,CAACoC,IAAI,GAAG,CAAC,iBACrB/C,OAAA;MAAKqE,SAAS,EAAC,wFAAwF;MAAAC,QAAA,gBACrGtE,OAAA;QAAMqE,SAAS,EAAC,2BAA2B;QAAAC,QAAA,GACxC3D,aAAa,CAACoC,IAAI,EAAC,mBACtB;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACP1E,OAAA;QAAKqE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CtE,OAAA;UACE2E,OAAO,EAAEA,CAAA,KAAMzB,oBAAoB,CAAC,QAAQ,CAAE;UAC9CmB,SAAS,EAAC,iHAAiH;UAAAC,QAAA,gBAE3HtE,OAAA,CAACT,KAAK;YAAC8E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1E,OAAA;UACE2E,OAAO,EAAEA,CAAA,KAAMzB,oBAAoB,CAAC,SAAS,CAAE;UAC/CmB,SAAS,EAAC,mHAAmH;UAAAC,QAAA,gBAE7HtE,OAAA,CAACR,QAAQ;YAAC6E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1E,OAAA;UACE2E,OAAO,EAAEA,CAAA,KAAMzB,oBAAoB,CAAC,QAAQ,CAAE;UAC9CmB,SAAS,EAAC,6GAA6G;UAAAC,QAAA,gBAEvHtE,OAAA,CAACV,MAAM;YAAC+E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1E,OAAA;MAAKqE,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvB9C,aAAa,CAACwB,MAAM,KAAK,CAAC,gBACzBhD,OAAA;QAAKqE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtE,OAAA,CAACb,MAAM;UAACkF,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtD1E,OAAA;UAAIqE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E1E,OAAA;UAAGqE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACtC7D,UAAU,IAAIK,UAAU,KAAK,KAAK,IAAIE,cAAc,KAAK,KAAK,IAAIE,YAAY,KAAK,KAAK,GACrF,sCAAsC,GACtC;QAAoD;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAEN1E,OAAA,CAAAE,SAAA;QAAAoE,QAAA,gBAEEtE,OAAA;UAAKqE,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAC5FtE,OAAA;YACE2E,OAAO,EAAE7B,eAAgB;YACzBuB,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAE3D3D,aAAa,CAACoC,IAAI,KAAKvB,aAAa,CAACwB,MAAM,gBAC1ChD,OAAA,CAACL,WAAW;cAAC0E,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEnC1E,OAAA,CAACJ,MAAM;cAACyE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC9B;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACT1E,OAAA;YAAMqE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,aAC3B,EAAC9C,aAAa,CAACwB,MAAM,EAAC,QACnC;UAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAGLlD,aAAa,CAACyB,GAAG,CAAEtB,IAAI,iBACtB3B,OAAA;UAEEqE,SAAS,EAAE,kFACT1D,aAAa,CAACgC,GAAG,CAAChB,IAAI,CAACM,EAAE,CAAC,GACtB,gCAAgC,GAChC,uCAAuC,EAC1C;UAAAqC,QAAA,eAEHtE,OAAA;YAAKqE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CtE,OAAA;cAAKqE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDtE,OAAA;gBACE2E,OAAO,EAAEA,CAAA,KAAMnC,gBAAgB,CAACb,IAAI,CAACM,EAAE,CAAE;gBACzCoC,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAEhE3D,aAAa,CAACgC,GAAG,CAAChB,IAAI,CAACM,EAAE,CAAC,gBACzBjC,OAAA,CAACL,WAAW;kBAAC0E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEnC1E,OAAA,CAACJ,MAAM;kBAACyE,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC9B;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAET1E,OAAA;gBAAKqE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBtE,OAAA;kBAAKqE,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/CtE,OAAA;oBAAIqE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAE3C,IAAI,CAACE;kBAAI;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjE1E,OAAA;oBAAMqE,SAAS,EAAE,uEAAuEF,gBAAgB,CAACxC,IAAI,CAACU,QAAQ,CAAC,EAAG;oBAAAiC,QAAA,EACvH3C,IAAI,CAACU;kBAAQ;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACP1E,OAAA;oBAAMqE,SAAS,EAAE,uEAAuED,YAAY,CAACzC,IAAI,CAACQ,IAAI,CAAC,EAAG;oBAAAmC,QAAA,EAC/G3C,IAAI,CAACQ;kBAAI;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN1E,OAAA;kBAAGqE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE3C,IAAI,CAACK;gBAAW;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChE1E,OAAA;kBAAKqE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,MAAI,EAAC3C,IAAI,CAACM,EAAE;gBAAA;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAE/D1E,OAAA;kBAAKqE,SAAS,EAAC,WAAW;kBAAAC,QAAA,GACvB3C,IAAI,CAACsD,UAAU,IAAItD,IAAI,CAACsD,UAAU,CAACjC,MAAM,GAAG,CAAC,iBAC5ChD,OAAA;oBAAKqE,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtBtE,OAAA;sBAAMqE,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1D1E,OAAA;sBAAMqE,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAE3C,IAAI,CAACsD,UAAU,CAACC,IAAI,CAAC,IAAI;oBAAC;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CACN,EACA/C,IAAI,CAACwD,OAAO,iBACXnF,OAAA;oBAAKqE,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtBtE,OAAA;sBAAMqE,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3D1E,OAAA;sBAAMqE,SAAS,EAAC,oFAAoF;sBAAAC,QAAA,EAAE3C,IAAI,CAACwD;oBAAO;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvH,CACN,eACD1E,OAAA;oBAAKqE,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtBtE,OAAA;sBAAMqE,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxD1E,OAAA;sBAAMqE,SAAS,EAAC,qFAAqF;sBAAAC,QAAA,EAAE3C,IAAI,CAACyD;oBAAS;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1E,OAAA;cAAKqE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CtE,OAAA;gBAAKqE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAC/B3C,IAAI,CAACY,OAAO,gBACXvC,OAAA,CAAAE,SAAA;kBAAAoE,QAAA,gBACEtE,OAAA;oBAAKqE,SAAS,EAAC;kBAAsD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5E1E,OAAA;oBAAMqE,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eACnE,CAAC,gBAEH1E,OAAA,CAAAE,SAAA;kBAAAoE,QAAA,gBACEtE,OAAA;oBAAKqE,SAAS,EAAC;kBAAuC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7D1E,OAAA;oBAAMqE,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eACnE;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1E,OAAA;gBAAKqE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBtE,OAAA;kBACE2E,OAAO,EAAEA,CAAA,KAAMpD,iBAAiB,CAACD,cAAc,KAAKK,IAAI,CAACM,EAAE,GAAG,IAAI,GAAGN,IAAI,CAACM,EAAE,CAAE;kBAC9EoC,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eAEhEtE,OAAA,CAACN,YAAY;oBAAC2E,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,EAERpD,cAAc,KAAKK,IAAI,CAACM,EAAE,iBACzBjC,OAAA;kBAAKqE,SAAS,EAAC,yFAAyF;kBAAAC,QAAA,gBACtGtE,OAAA;oBACE2E,OAAO,EAAEA,CAAA,KAAM;sBACbrE,UAAU,CAACqB,IAAI,CAAC;sBAChBJ,iBAAiB,CAAC,IAAI,CAAC;oBACzB,CAAE;oBACF8C,SAAS,EAAC,wFAAwF;oBAAAC,QAAA,gBAElGtE,OAAA,CAACX,IAAI;sBAACgF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5B1E,OAAA;sBAAAsE,QAAA,EAAM;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACT1E,OAAA;oBACE2E,OAAO,EAAEA,CAAA,KAAM;sBACbZ,gBAAgB,CAACpC,IAAI,CAACM,EAAE,EAAE,CAACN,IAAI,CAACY,OAAO,CAAC;sBACxChB,iBAAiB,CAAC,IAAI,CAAC;oBACzB,CAAE;oBACF8C,SAAS,EAAC,wFAAwF;oBAAAC,QAAA,GAEjG3C,IAAI,CAACY,OAAO,gBAAGvC,OAAA,CAACR,QAAQ;sBAAC6E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACT,KAAK;sBAAC8E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChF1E,OAAA;sBAAAsE,QAAA,EAAO3C,IAAI,CAACY,OAAO,GAAG,SAAS,GAAG;oBAAQ;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACT1E,OAAA;oBACE2E,OAAO,EAAEA,CAAA,KAAM;sBACbV,gBAAgB,CAACtC,IAAI,CAACM,EAAE,CAAC;sBACzBV,iBAAiB,CAAC,IAAI,CAAC;oBACzB,CAAE;oBACF8C,SAAS,EAAC,uFAAuF;oBAAAC,QAAA,gBAEjGtE,OAAA,CAACV,MAAM;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9B1E,OAAA;sBAAAsE,QAAA,EAAM;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjHD/C,IAAI,CAACM,EAAE;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkHT,CACN,CAAC;MAAA,eACF;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CAjYIL,SAAmC;AAAAkF,EAAA,GAAnClF,SAAmC;AAmYzC,eAAeA,SAAS;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}