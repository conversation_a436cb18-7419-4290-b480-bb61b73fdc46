{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport { Settings, BarChart3, List, RefreshCw, Power, PowerOff, Menu, X } from 'lucide-react';\nimport { sanitizationApi } from './services/api';\nimport Dashboard from './components/Dashboard';\nimport RulesList from './components/RulesList';\nimport RuleEditor from './components/RuleEditor';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [config, setConfig] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [toggling, setToggling] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // Rule Editor State\n  const [ruleEditorOpen, setRuleEditorOpen] = useState(false);\n  const [editingRule, setEditingRule] = useState();\n  const [editorMode, setEditorMode] = useState('create');\n  useEffect(() => {\n    fetchRules();\n  }, []);\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response);\n    } catch (error) {\n      toast.error('Failed to fetch rules');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('Rules reloaded successfully');\n      await fetchRules();\n    } catch (error) {\n      toast.error('Failed to reload rules');\n      console.error('Error reloading rules:', error);\n    }\n  };\n  const handleToggleGlobal = async () => {\n    if (!config) return;\n    try {\n      setToggling(true);\n      const newEnabled = !config.enabled;\n      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);\n      setConfig(prev => prev ? {\n        ...prev,\n        enabled: response.enabled\n      } : null);\n      toast.success(newEnabled ? 'Global sanitization enabled' : 'Global sanitization disabled');\n    } catch (error) {\n      toast.error('Failed to toggle global switch');\n      console.error('Error toggling global switch:', error);\n    } finally {\n      setToggling(false);\n    }\n  };\n  const handleCreateRule = () => {\n    setEditingRule(undefined);\n    setEditorMode('create');\n    setRuleEditorOpen(true);\n  };\n  const handleEditRule = rule => {\n    setEditingRule(rule);\n    setEditorMode('edit');\n    setRuleEditorOpen(true);\n  };\n  const handleRuleSaved = () => {\n    fetchRules();\n  };\n  const navigation = [{\n    id: 'dashboard',\n    name: 'Dashboard',\n    icon: BarChart3\n  }, {\n    id: 'rules',\n    name: 'Rules',\n    icon: List\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-black flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 mt-4\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-black text-white\",\n    children: [sidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n      onClick: () => setSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 border-r border-gray-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b border-gray-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2 bg-blue-600 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Settings, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg font-bold text-white\",\n              children: \"Sanitization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-400\",\n              children: \"Config Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSidebarOpen(false),\n          className: \"lg:hidden p-2 hover:bg-gray-800 rounded-full transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"h-5 w-5 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"p-4 space-y-2\",\n        children: navigation.map(item => {\n          const Icon = item.icon;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setActiveTab(item.id);\n              setSidebarOpen(false);\n            },\n            className: `w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${activeTab === item.id ? 'bg-blue-600 text-white' : 'text-gray-300 hover:bg-gray-800 hover:text-white'}`,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-800\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-400\",\n              children: \"Global Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-sm font-medium ${config !== null && config !== void 0 && config.enabled ? 'text-green-400' : 'text-red-400'}`,\n              children: config !== null && config !== void 0 && config.enabled ? 'Enabled' : 'Disabled'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleReload,\n              className: \"flex-1 inline-flex items-center justify-center px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), \"Reload\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleToggleGlobal,\n              disabled: toggling,\n              className: `flex-1 inline-flex items-center justify-center px-3 py-2 rounded-lg text-sm transition-colors ${config !== null && config !== void 0 && config.enabled ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white'} ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`,\n              children: [config !== null && config !== void 0 && config.enabled ? /*#__PURE__*/_jsxDEV(PowerOff, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 36\n              }, this) : /*#__PURE__*/_jsxDEV(Power, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 76\n              }, this), toggling ? 'Wait...' : config !== null && config !== void 0 && config.enabled ? 'Disable' : 'Enable']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:ml-64\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden flex items-center justify-between p-4 border-b border-gray-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSidebarOpen(true),\n          className: \"p-2 hover:bg-gray-800 rounded-full transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(Menu, {\n            className: \"h-6 w-6 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2 bg-blue-600 rounded-full\",\n            children: /*#__PURE__*/_jsxDEV(Settings, {\n              className: \"h-5 w-5 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-bold text-white\",\n            children: \"Sanitization\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"min-h-screen\",\n        children: [activeTab === 'dashboard' && /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 41\n        }, this), activeTab === 'rules' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: /*#__PURE__*/_jsxDEV(RulesList, {\n            config: config,\n            onRefresh: fetchRules,\n            onEditRule: handleEditRule,\n            onCreateRule: handleCreateRule\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RuleEditor, {\n      rule: editingRule,\n      isOpen: ruleEditorOpen,\n      onClose: () => setRuleEditorOpen(false),\n      onSave: handleRuleSaved,\n      mode: editorMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\",\n      toastOptions: {\n        style: {\n          background: '#1f2937',\n          color: '#f3f4f6',\n          border: '1px solid #374151'\n        },\n        success: {\n          iconTheme: {\n            primary: '#10b981',\n            secondary: '#1f2937'\n          }\n        },\n        error: {\n          iconTheme: {\n            primary: '#ef4444',\n            secondary: '#1f2937'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"q6Y9Uu2w4fOXinZ8rzeyFKFmtLc=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Toaster", "Settings", "BarChart3", "List", "RefreshCw", "Power", "PowerOff", "<PERSON><PERSON>", "X", "sanitizationApi", "Dashboard", "RulesList", "RuleEditor", "toast", "jsxDEV", "_jsxDEV", "App", "_s", "activeTab", "setActiveTab", "config", "setConfig", "loading", "setLoading", "toggling", "setToggling", "sidebarOpen", "setSidebarOpen", "ruleEditorOpen", "setRuleEditorOpen", "editingRule", "setEditingRule", "editor<PERSON><PERSON>", "setEditorMode", "fetchRules", "response", "getRules", "error", "console", "handleReload", "reloadRules", "success", "handleToggleGlobal", "newEnabled", "enabled", "toggleGlobalSwitch", "prev", "handleCreateRule", "undefined", "handleEditRule", "rule", "handleRuleSaved", "navigation", "id", "name", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "item", "Icon", "disabled", "onRefresh", "onEditRule", "onCreateRule", "isOpen", "onClose", "onSave", "mode", "position", "toastOptions", "style", "background", "color", "border", "iconTheme", "primary", "secondary", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport {\n  Settings,\n  BarChart3,\n  List,\n  RefreshCw,\n  Power,\n  PowerOff,\n  Menu,\n  X\n} from 'lucide-react';\nimport { sanitizationApi } from './services/api';\nimport { SanitizationConfig, SanitizationRule } from './types';\nimport Dashboard from './components/Dashboard';\nimport RulesList from './components/RulesList';\nimport RuleEditor from './components/RuleEditor';\nimport toast from 'react-hot-toast';\n\ntype ActiveTab = 'dashboard' | 'rules';\n\nfunction App() {\n  const [activeTab, setActiveTab] = useState<ActiveTab>('dashboard');\n  const [config, setConfig] = useState<SanitizationConfig | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [toggling, setToggling] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // Rule Editor State\n  const [ruleEditorOpen, setRuleEditorOpen] = useState(false);\n  const [editingRule, setEditingRule] = useState<SanitizationRule | undefined>();\n  const [editorMode, setEditorMode] = useState<'create' | 'edit'>('create');\n\n  useEffect(() => {\n    fetchRules();\n  }, []);\n\n  const fetchRules = async () => {\n    try {\n      setLoading(true);\n      const response = await sanitizationApi.getRules();\n      setConfig(response);\n    } catch (error) {\n      toast.error('Failed to fetch rules');\n      console.error('Error fetching rules:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReload = async () => {\n    try {\n      await sanitizationApi.reloadRules();\n      toast.success('Rules reloaded successfully');\n      await fetchRules();\n    } catch (error) {\n      toast.error('Failed to reload rules');\n      console.error('Error reloading rules:', error);\n    }\n  };\n\n  const handleToggleGlobal = async () => {\n    if (!config) return;\n\n    try {\n      setToggling(true);\n      const newEnabled = !config.enabled;\n      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);\n      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);\n      toast.success(newEnabled ? 'Global sanitization enabled' : 'Global sanitization disabled');\n    } catch (error) {\n      toast.error('Failed to toggle global switch');\n      console.error('Error toggling global switch:', error);\n    } finally {\n      setToggling(false);\n    }\n  };\n\n  const handleCreateRule = () => {\n    setEditingRule(undefined);\n    setEditorMode('create');\n    setRuleEditorOpen(true);\n  };\n\n  const handleEditRule = (rule: SanitizationRule) => {\n    setEditingRule(rule);\n    setEditorMode('edit');\n    setRuleEditorOpen(true);\n  };\n\n  const handleRuleSaved = () => {\n    fetchRules();\n  };\n\n  const navigation = [\n    { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },\n    { id: 'rules', name: 'Rules', icon: List },\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"></div>\n          <p className=\"text-gray-400 mt-4\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-black text-white\">\n      {/* Mobile sidebar backdrop */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 border-r border-gray-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${\n        sidebarOpen ? 'translate-x-0' : '-translate-x-full'\n      }`}>\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-800\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"p-2 bg-blue-600 rounded-full\">\n              <Settings className=\"h-6 w-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-lg font-bold text-white\">Sanitization</h1>\n              <p className=\"text-xs text-gray-400\">Config Service</p>\n            </div>\n          </div>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden p-2 hover:bg-gray-800 rounded-full transition-colors\"\n          >\n            <X className=\"h-5 w-5 text-gray-400\" />\n          </button>\n        </div>\n\n        <nav className=\"p-4 space-y-2\">\n          {navigation.map((item) => {\n            const Icon = item.icon;\n            return (\n              <button\n                key={item.id}\n                onClick={() => {\n                  setActiveTab(item.id as ActiveTab);\n                  setSidebarOpen(false);\n                }}\n                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${\n                  activeTab === item.id\n                    ? 'bg-blue-600 text-white'\n                    : 'text-gray-300 hover:bg-gray-800 hover:text-white'\n                }`}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span className=\"font-medium\">{item.name}</span>\n              </button>\n            );\n          })}\n        </nav>\n\n        {/* Global Status */}\n        <div className=\"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-800\">\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-400\">Global Status</span>\n              <span className={`text-sm font-medium ${\n                config?.enabled ? 'text-green-400' : 'text-red-400'\n              }`}>\n                {config?.enabled ? 'Enabled' : 'Disabled'}\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={handleReload}\n                className=\"flex-1 inline-flex items-center justify-center px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Reload\n              </button>\n              <button\n                onClick={handleToggleGlobal}\n                disabled={toggling}\n                className={`flex-1 inline-flex items-center justify-center px-3 py-2 rounded-lg text-sm transition-colors ${\n                  config?.enabled\n                    ? 'bg-red-600 hover:bg-red-700 text-white'\n                    : 'bg-green-600 hover:bg-green-700 text-white'\n                } ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`}\n              >\n                {config?.enabled ? <PowerOff className=\"h-4 w-4 mr-2\" /> : <Power className=\"h-4 w-4 mr-2\" />}\n                {toggling ? 'Wait...' : (config?.enabled ? 'Disable' : 'Enable')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:ml-64\">\n        {/* Mobile header */}\n        <div className=\"lg:hidden flex items-center justify-between p-4 border-b border-gray-800\">\n          <button\n            onClick={() => setSidebarOpen(true)}\n            className=\"p-2 hover:bg-gray-800 rounded-full transition-colors\"\n          >\n            <Menu className=\"h-6 w-6 text-gray-400\" />\n          </button>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"p-2 bg-blue-600 rounded-full\">\n              <Settings className=\"h-5 w-5 text-white\" />\n            </div>\n            <span className=\"font-bold text-white\">Sanitization</span>\n          </div>\n          <div className=\"w-10\" /> {/* Spacer */}\n        </div>\n\n        {/* Page content */}\n        <main className=\"min-h-screen\">\n          {activeTab === 'dashboard' && <Dashboard />}\n          {activeTab === 'rules' && (\n            <div className=\"p-6\">\n              <RulesList\n                config={config}\n                onRefresh={fetchRules}\n                onEditRule={handleEditRule}\n                onCreateRule={handleCreateRule}\n              />\n            </div>\n          )}\n        </main>\n      </div>\n\n      {/* Rule Editor Modal */}\n      <RuleEditor\n        rule={editingRule}\n        isOpen={ruleEditorOpen}\n        onClose={() => setRuleEditorOpen(false)}\n        onSave={handleRuleSaved}\n        mode={editorMode}\n      />\n\n      {/* Toast notifications */}\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          style: {\n            background: '#1f2937',\n            color: '#f3f4f6',\n            border: '1px solid #374151',\n          },\n          success: {\n            iconTheme: {\n              primary: '#10b981',\n              secondary: '#1f2937',\n            },\n          },\n          error: {\n            iconTheme: {\n              primary: '#ef4444',\n              secondary: '#1f2937',\n            },\n          },\n        }}\n      />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SACEC,QAAQ,EACRC,SAAS,EACTC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,CAAC,QACI,cAAc;AACrB,SAASC,eAAe,QAAQ,gBAAgB;AAEhD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAIpC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAY,WAAW,CAAC;EAClE,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAA4B,IAAI,CAAC;EACrE,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAA+B,CAAC;EAC9E,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAoB,QAAQ,CAAC;EAEzEC,SAAS,CAAC,MAAM;IACdmC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMY,QAAQ,GAAG,MAAM1B,eAAe,CAAC2B,QAAQ,CAAC,CAAC;MACjDf,SAAS,CAACc,QAAQ,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdxB,KAAK,CAACwB,KAAK,CAAC,uBAAuB,CAAC;MACpCC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAM9B,eAAe,CAAC+B,WAAW,CAAC,CAAC;MACnC3B,KAAK,CAAC4B,OAAO,CAAC,6BAA6B,CAAC;MAC5C,MAAMP,UAAU,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdxB,KAAK,CAACwB,KAAK,CAAC,wBAAwB,CAAC;MACrCC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACtB,MAAM,EAAE;IAEb,IAAI;MACFK,WAAW,CAAC,IAAI,CAAC;MACjB,MAAMkB,UAAU,GAAG,CAACvB,MAAM,CAACwB,OAAO;MAClC,MAAMT,QAAQ,GAAG,MAAM1B,eAAe,CAACoC,kBAAkB,CAACF,UAAU,CAAC;MACrEtB,SAAS,CAACyB,IAAI,IAAIA,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAEF,OAAO,EAAET,QAAQ,CAACS;MAAQ,CAAC,GAAG,IAAI,CAAC;MACvE/B,KAAK,CAAC4B,OAAO,CAACE,UAAU,GAAG,6BAA6B,GAAG,8BAA8B,CAAC;IAC5F,CAAC,CAAC,OAAON,KAAK,EAAE;MACdxB,KAAK,CAACwB,KAAK,CAAC,gCAAgC,CAAC;MAC7CC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRZ,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhB,cAAc,CAACiB,SAAS,CAAC;IACzBf,aAAa,CAAC,QAAQ,CAAC;IACvBJ,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoB,cAAc,GAAIC,IAAsB,IAAK;IACjDnB,cAAc,CAACmB,IAAI,CAAC;IACpBjB,aAAa,CAAC,MAAM,CAAC;IACrBJ,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMsB,eAAe,GAAGA,CAAA,KAAM;IAC5BjB,UAAU,CAAC,CAAC;EACd,CAAC;EAED,MAAMkB,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAErD;EAAU,CAAC,EACvD;IAAEmD,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAEpD;EAAK,CAAC,CAC3C;EAED,IAAImB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKyC,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrE1C,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1C,OAAA;UAAKyC,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9F9C,OAAA;UAAGyC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9C,OAAA;IAAKyC,SAAS,EAAC,kCAAkC;IAAAC,QAAA,GAE9C/B,WAAW,iBACVX,OAAA;MACEyC,SAAS,EAAC,qDAAqD;MAC/DM,OAAO,EAAEA,CAAA,KAAMnC,cAAc,CAAC,KAAK;IAAE;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF,eAGD9C,OAAA;MAAKyC,SAAS,EAAE,kJACd9B,WAAW,GAAG,eAAe,GAAG,mBAAmB,EAClD;MAAA+B,QAAA,gBACD1C,OAAA;QAAKyC,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7E1C,OAAA;UAAKyC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C1C,OAAA;YAAKyC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C1C,OAAA,CAACd,QAAQ;cAACuD,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACN9C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAIyC,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9D9C,OAAA;cAAGyC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9C,OAAA;UACE+C,OAAO,EAAEA,CAAA,KAAMnC,cAAc,CAAC,KAAK,CAAE;UACrC6B,SAAS,EAAC,gEAAgE;UAAAC,QAAA,eAE1E1C,OAAA,CAACP,CAAC;YAACgD,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9C,OAAA;QAAKyC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BL,UAAU,CAACW,GAAG,CAAEC,IAAI,IAAK;UACxB,MAAMC,IAAI,GAAGD,IAAI,CAACT,IAAI;UACtB,oBACExC,OAAA;YAEE+C,OAAO,EAAEA,CAAA,KAAM;cACb3C,YAAY,CAAC6C,IAAI,CAACX,EAAe,CAAC;cAClC1B,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YACF6B,SAAS,EAAE,6EACTtC,SAAS,KAAK8C,IAAI,CAACX,EAAE,GACjB,wBAAwB,GACxB,kDAAkD,EACrD;YAAAI,QAAA,gBAEH1C,OAAA,CAACkD,IAAI;cAACT,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5B9C,OAAA;cAAMyC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEO,IAAI,CAACV;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAZ3CG,IAAI,CAACX,EAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaN,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN9C,OAAA;QAAKyC,SAAS,EAAC,+DAA+D;QAAAC,QAAA,eAC5E1C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1C,OAAA;YAAKyC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD1C,OAAA;cAAMyC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5D9C,OAAA;cAAMyC,SAAS,EAAE,uBACfpC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEwB,OAAO,GAAG,gBAAgB,GAAG,cAAc,EAClD;cAAAa,QAAA,EACArC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEwB,OAAO,GAAG,SAAS,GAAG;YAAU;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN9C,OAAA;YAAKyC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C1C,OAAA;cACE+C,OAAO,EAAEvB,YAAa;cACtBiB,SAAS,EAAC,wIAAwI;cAAAC,QAAA,gBAElJ1C,OAAA,CAACX,SAAS;gBAACoD,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9C,OAAA;cACE+C,OAAO,EAAEpB,kBAAmB;cAC5BwB,QAAQ,EAAE1C,QAAS;cACnBgC,SAAS,EAAE,iGACTpC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEwB,OAAO,GACX,wCAAwC,GACxC,4CAA4C,IAC9CpB,QAAQ,GAAG,+BAA+B,GAAG,EAAE,EAAG;cAAAiC,QAAA,GAErDrC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEwB,OAAO,gBAAG7B,OAAA,CAACT,QAAQ;gBAACkD,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG9C,OAAA,CAACV,KAAK;gBAACmD,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC5FrC,QAAQ,GAAG,SAAS,GAAIJ,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEwB,OAAO,GAAG,SAAS,GAAG,QAAS;YAAA;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA;MAAKyC,SAAS,EAAC,UAAU;MAAAC,QAAA,gBAEvB1C,OAAA;QAAKyC,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBACvF1C,OAAA;UACE+C,OAAO,EAAEA,CAAA,KAAMnC,cAAc,CAAC,IAAI,CAAE;UACpC6B,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eAEhE1C,OAAA,CAACR,IAAI;YAACiD,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACT9C,OAAA;UAAKyC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C1C,OAAA;YAAKyC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C1C,OAAA,CAACd,QAAQ;cAACuD,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACN9C,OAAA;YAAMyC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,KAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAGN9C,OAAA;QAAMyC,SAAS,EAAC,cAAc;QAAAC,QAAA,GAC3BvC,SAAS,KAAK,WAAW,iBAAIH,OAAA,CAACL,SAAS;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC1C3C,SAAS,KAAK,OAAO,iBACpBH,OAAA;UAAKyC,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClB1C,OAAA,CAACJ,SAAS;YACRS,MAAM,EAAEA,MAAO;YACf+C,SAAS,EAAEjC,UAAW;YACtBkC,UAAU,EAAEnB,cAAe;YAC3BoB,YAAY,EAAEtB;UAAiB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN9C,OAAA,CAACH,UAAU;MACTsC,IAAI,EAAEpB,WAAY;MAClBwC,MAAM,EAAE1C,cAAe;MACvB2C,OAAO,EAAEA,CAAA,KAAM1C,iBAAiB,CAAC,KAAK,CAAE;MACxC2C,MAAM,EAAErB,eAAgB;MACxBsB,IAAI,EAAEzC;IAAW;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGF9C,OAAA,CAACf,OAAO;MACN0E,QAAQ,EAAC,WAAW;MACpBC,YAAY,EAAE;QACZC,KAAK,EAAE;UACLC,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE,SAAS;UAChBC,MAAM,EAAE;QACV,CAAC;QACDtC,OAAO,EAAE;UACPuC,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF,CAAC;QACD7C,KAAK,EAAE;UACL2C,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF;MACF;IAAE;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAC5C,EAAA,CAzPQD,GAAG;AAAAmE,EAAA,GAAHnE,GAAG;AA2PZ,eAAeA,GAAG;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}