[{"/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/index.tsx": "1", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx": "3", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx": "4", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts": "5", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RulesList.tsx": "6", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RuleEditor.tsx": "7", "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Dashboard.tsx": "8"}, {"size": 554, "mtime": 1752758697871, "results": "9", "hashOfConfig": "10"}, {"size": 425, "mtime": 1752758697872, "results": "11", "hashOfConfig": "10"}, {"size": 9029, "mtime": 1752797818250, "results": "12", "hashOfConfig": "10"}, {"size": 10508, "mtime": 1752797185127, "results": "13", "hashOfConfig": "10"}, {"size": 4147, "mtime": 1752798289903, "results": "14", "hashOfConfig": "10"}, {"size": 19836, "mtime": 1752798333339, "results": "15", "hashOfConfig": "10"}, {"size": 13675, "mtime": 1752797684423, "results": "16", "hashOfConfig": "10"}, {"size": 10639, "mtime": 1752797727306, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1a6fn3w", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/index.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/App.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/SimpleRulesList.tsx", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/services/api.ts", [], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RulesList.tsx", ["42"], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/RuleEditor.tsx", ["43"], [], "/Users/<USER>/dev/work/lab/javaagent/sanitization-config-service/frontend/src/components/Dashboard.tsx", [], [], {"ruleId": "44", "severity": 1, "message": "45", "line": 1, "column": 27, "nodeType": "46", "messageId": "47", "endLine": 1, "endColumn": 36}, {"ruleId": "44", "severity": 1, "message": "48", "line": 2, "column": 42, "nodeType": "46", "messageId": "47", "endLine": 2, "endColumn": 53}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'CheckCircle' is defined but never used."]