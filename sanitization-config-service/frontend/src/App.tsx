import React, { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import {
  Settings,
  RefreshCw,
  Power,
  PowerOff
} from 'lucide-react';
import { sanitizationApi } from './services/api';
import { SanitizationConfig, SanitizationRule } from './types';
import RulesList from './components/RulesList';
import RuleEditor from './components/RuleEditor';
import toast from 'react-hot-toast';

function App() {
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [toggling, setToggling] = useState(false);

  // Rule Editor State
  const [ruleEditorOpen, setRuleEditorOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<SanitizationRule | undefined>();
  const [editorMode, setEditorMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    fetchRules();
  }, []);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await sanitizationApi.getRules();
      setConfig(response);
    } catch (error) {
      toast.error('Failed to fetch rules');
      console.error('Error fetching rules:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReload = async () => {
    try {
      await sanitizationApi.reloadRules();
      toast.success('Rules reloaded successfully');
      await fetchRules();
    } catch (error) {
      toast.error('Failed to reload rules');
      console.error('Error reloading rules:', error);
    }
  };

  const handleToggleGlobal = async () => {
    if (!config) return;

    try {
      setToggling(true);
      const newEnabled = !config.enabled;
      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);
      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);
      toast.success(newEnabled ? 'Global sanitization enabled' : 'Global sanitization disabled');
    } catch (error) {
      toast.error('Failed to toggle global switch');
      console.error('Error toggling global switch:', error);
    } finally {
      setToggling(false);
    }
  };

  const handleCreateRule = () => {
    setEditingRule(undefined);
    setEditorMode('create');
    setRuleEditorOpen(true);
  };

  const handleEditRule = (rule: SanitizationRule) => {
    setEditingRule(rule);
    setEditorMode('edit');
    setRuleEditorOpen(true);
  };

  const handleRuleSaved = () => {
    fetchRules();
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-gray-600 mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Settings className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">脱敏规则管理</h1>
                <p className="text-sm text-gray-500">Data Sanitization Rules</p>
              </div>
            </div>

            {/* Global Controls */}
            <div className="flex items-center space-x-4">
              {/* Global Status */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">全局脱敏:</span>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${
                    config?.enabled ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span className={`text-sm font-medium ${
                    config?.enabled ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {config?.enabled ? '已启用' : '已禁用'}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleReload}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重载配置
                </button>
                <button
                  onClick={handleToggleGlobal}
                  disabled={toggling}
                  className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    config?.enabled
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  } ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {config?.enabled ? <PowerOff className="h-4 w-4 mr-2" /> : <Power className="h-4 w-4 mr-2" />}
                  {toggling ? '处理中...' : (config?.enabled ? '禁用脱敏' : '启用脱敏')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <RulesList
          config={config}
          onRefresh={fetchRules}
          onEditRule={handleEditRule}
          onCreateRule={handleCreateRule}
        />
      </main>

      {/* Rule Editor Modal */}
      <RuleEditor
        rule={editingRule}
        isOpen={ruleEditorOpen}
        onClose={() => setRuleEditorOpen(false)}
        onSave={handleRuleSaved}
        mode={editorMode}
      />

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: '#1f2937',
            color: '#f3f4f6',
            border: '1px solid #374151',
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#1f2937',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#1f2937',
            },
          },
        }}
      />
    </div>
  );
}

export default App;
