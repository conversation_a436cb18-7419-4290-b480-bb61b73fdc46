import React, { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import {
  Settings,
  BarChart3,
  List,
  RefreshCw,
  Power,
  PowerOff,
  Menu,
  X
} from 'lucide-react';
import { sanitizationApi } from './services/api';
import { SanitizationConfig, SanitizationRule } from './types';
import Dashboard from './components/Dashboard';
import RulesList from './components/RulesList';
import RuleEditor from './components/RuleEditor';
import toast from 'react-hot-toast';

type ActiveTab = 'dashboard' | 'rules';

function App() {
  const [activeTab, setActiveTab] = useState<ActiveTab>('dashboard');
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [toggling, setToggling] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Rule Editor State
  const [ruleEditorOpen, setRuleEditorOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<SanitizationRule | undefined>();
  const [editorMode, setEditorMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    fetchRules();
  }, []);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await sanitizationApi.getRules();
      setConfig(response);
    } catch (error) {
      toast.error('Failed to fetch rules');
      console.error('Error fetching rules:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReload = async () => {
    try {
      await sanitizationApi.reloadRules();
      toast.success('Rules reloaded successfully');
      await fetchRules();
    } catch (error) {
      toast.error('Failed to reload rules');
      console.error('Error reloading rules:', error);
    }
  };

  const handleToggleGlobal = async () => {
    if (!config) return;

    try {
      setToggling(true);
      const newEnabled = !config.enabled;
      const response = await sanitizationApi.toggleGlobalSwitch(newEnabled);
      setConfig(prev => prev ? { ...prev, enabled: response.enabled } : null);
      toast.success(newEnabled ? 'Global sanitization enabled' : 'Global sanitization disabled');
    } catch (error) {
      toast.error('Failed to toggle global switch');
      console.error('Error toggling global switch:', error);
    } finally {
      setToggling(false);
    }
  };

  const handleCreateRule = () => {
    setEditingRule(undefined);
    setEditorMode('create');
    setRuleEditorOpen(true);
  };

  const handleEditRule = (rule: SanitizationRule) => {
    setEditingRule(rule);
    setEditorMode('edit');
    setRuleEditorOpen(true);
  };

  const handleRuleSaved = () => {
    fetchRules();
  };

  const navigation = [
    { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
    { id: 'rules', name: 'Rules', icon: List },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-gray-400 mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 border-r border-gray-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-600 rounded-full">
              <Settings className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-white">Sanitization</h1>
              <p className="text-xs text-gray-400">Config Service</p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 hover:bg-gray-800 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        </div>

        <nav className="p-4 space-y-2">
          {navigation.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => {
                  setActiveTab(item.id as ActiveTab);
                  setSidebarOpen(false);
                }}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                  activeTab === item.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span className="font-medium">{item.name}</span>
              </button>
            );
          })}
        </nav>

        {/* Global Status */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-800">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Global Status</span>
              <span className={`text-sm font-medium ${
                config?.enabled ? 'text-green-400' : 'text-red-400'
              }`}>
                {config?.enabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleReload}
                className="flex-1 inline-flex items-center justify-center px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload
              </button>
              <button
                onClick={handleToggleGlobal}
                disabled={toggling}
                className={`flex-1 inline-flex items-center justify-center px-3 py-2 rounded-lg text-sm transition-colors ${
                  config?.enabled
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-green-600 hover:bg-green-700 text-white'
                } ${toggling ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {config?.enabled ? <PowerOff className="h-4 w-4 mr-2" /> : <Power className="h-4 w-4 mr-2" />}
                {toggling ? 'Wait...' : (config?.enabled ? 'Disable' : 'Enable')}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Mobile header */}
        <div className="lg:hidden flex items-center justify-between p-4 border-b border-gray-800">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 hover:bg-gray-800 rounded-full transition-colors"
          >
            <Menu className="h-6 w-6 text-gray-400" />
          </button>
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-blue-600 rounded-full">
              <Settings className="h-5 w-5 text-white" />
            </div>
            <span className="font-bold text-white">Sanitization</span>
          </div>
          <div className="w-10" /> {/* Spacer */}
        </div>

        {/* Page content */}
        <main className="min-h-screen">
          {activeTab === 'dashboard' && <Dashboard />}
          {activeTab === 'rules' && (
            <div className="p-6">
              <RulesList
                config={config}
                onRefresh={fetchRules}
                onEditRule={handleEditRule}
                onCreateRule={handleCreateRule}
              />
            </div>
          )}
        </main>
      </div>

      {/* Rule Editor Modal */}
      <RuleEditor
        rule={editingRule}
        isOpen={ruleEditorOpen}
        onClose={() => setRuleEditorOpen(false)}
        onSave={handleRuleSaved}
        mode={editorMode}
      />

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: '#1f2937',
            color: '#f3f4f6',
            border: '1px solid #374151',
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#1f2937',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#1f2937',
            },
          },
        }}
      />
    </div>
  );
}

export default App;
