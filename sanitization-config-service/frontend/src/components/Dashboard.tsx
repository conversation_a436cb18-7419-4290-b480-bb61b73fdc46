import React, { useState, useEffect } from 'react';
import { BarChart3, Activity, Shield, AlertTriangle, TrendingUp, Clock } from 'lucide-react';
import { sanitizationApi } from '../services/api';
import { MetricsResponse, SanitizationConfig } from '../types';
import toast from 'react-hot-toast';

const Dashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<MetricsResponse | null>(null);
  const [config, setConfig] = useState<SanitizationConfig | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [metricsResponse, configResponse] = await Promise.all([
        sanitizationApi.getMetrics(),
        sanitizationApi.getRules()
      ]);
      setMetrics(metricsResponse);
      setConfig(configResponse);
    } catch (error) {
      toast.error('Failed to fetch dashboard data');
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-xl p-6 h-32"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-xl p-6 h-64"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!metrics || !config) {
    return (
      <div className="p-6 text-center">
        <div className="text-gray-400">Failed to load dashboard data</div>
      </div>
    );
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-400';
      case 'HIGH': return 'text-orange-400';
      case 'MEDIUM': return 'text-yellow-400';
      case 'LOW': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'FIELD_NAME': return 'text-blue-400';
      case 'PATTERN': return 'text-purple-400';
      case 'CONTENT_TYPE': return 'text-indigo-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Dashboard</h1>
          <p className="text-gray-400 mt-1">Overview of sanitization rules and metrics</p>
        </div>
        <button
          onClick={fetchData}
          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Activity className="h-4 w-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Rules</p>
              <p className="text-2xl font-bold text-white">{metrics.totalRules}</p>
            </div>
            <div className="p-3 bg-blue-600 rounded-full">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gray-900 border border-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Enabled Rules</p>
              <p className="text-2xl font-bold text-green-400">{metrics.enabledRules}</p>
            </div>
            <div className="p-3 bg-green-600 rounded-full">
              <Shield className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gray-900 border border-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Disabled Rules</p>
              <p className="text-2xl font-bold text-red-400">{metrics.disabledRules}</p>
            </div>
            <div className="p-3 bg-red-600 rounded-full">
              <AlertTriangle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-gray-900 border border-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Global Status</p>
              <p className={`text-2xl font-bold ${config.enabled ? 'text-green-400' : 'text-red-400'}`}>
                {config.enabled ? 'Enabled' : 'Disabled'}
              </p>
            </div>
            <div className={`p-3 rounded-full ${config.enabled ? 'bg-green-600' : 'bg-red-600'}`}>
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Rules by Type */}
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Rules by Type</h3>
          <div className="space-y-4">
            {Object.entries(metrics.rulesByType).map(([type, count]) => (
              <div key={type} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${getTypeColor(type).replace('text-', 'bg-')}`}></div>
                  <span className="text-gray-300">{type}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-32 bg-gray-800 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getTypeColor(type).replace('text-', 'bg-')}`}
                      style={{ width: `${(count / metrics.totalRules) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-white font-medium w-8 text-right">{count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Rules by Severity */}
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Rules by Severity</h3>
          <div className="space-y-4">
            {Object.entries(metrics.rulesBySeverity).map(([severity, count]) => (
              <div key={severity} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${getSeverityColor(severity).replace('text-', 'bg-')}`}></div>
                  <span className="text-gray-300">{severity}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-32 bg-gray-800 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getSeverityColor(severity).replace('text-', 'bg-')}`}
                      style={{ width: `${(count / metrics.totalRules) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-white font-medium w-8 text-right">{count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* System Information */}
      <div className="bg-gray-900 border border-gray-800 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">System Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="h-4 w-4 text-gray-400" />
              <span className="text-gray-400 text-sm">Last Updated</span>
            </div>
            <p className="text-white">{formatTimestamp(metrics.lastUpdated)}</p>
          </div>
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 className="h-4 w-4 text-gray-400" />
              <span className="text-gray-400 text-sm">Config Version</span>
            </div>
            <p className="text-white">{metrics.configVersion}</p>
          </div>
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="h-4 w-4 text-gray-400" />
              <span className="text-gray-400 text-sm">Markers Enabled</span>
            </div>
            <p className={`font-medium ${config.markersEnabled ? 'text-green-400' : 'text-red-400'}`}>
              {config.markersEnabled ? 'Yes' : 'No'}
            </p>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-gray-900 border border-gray-800 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Configuration Summary</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-800">
            <span className="text-gray-400">Global Sanitization</span>
            <span className={`font-medium ${config.enabled ? 'text-green-400' : 'text-red-400'}`}>
              {config.enabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
          <div className="flex items-center justify-between py-2 border-b border-gray-800">
            <span className="text-gray-400">Marker Format</span>
            <span className="text-white">{config.markerFormat}</span>
          </div>
          <div className="flex items-center justify-between py-2 border-b border-gray-800">
            <span className="text-gray-400">Configuration Version</span>
            <span className="text-white">{config.version}</span>
          </div>
          <div className="flex items-center justify-between py-2">
            <span className="text-gray-400">Last Modified</span>
            <span className="text-white">{formatTimestamp(config.timestamp)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
