import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Power, 
  PowerOff, 
  Filter,
  MoreVertical,
  CheckSquare,
  Square
} from 'lucide-react';
import { SanitizationRule, SanitizationConfig } from '../types';
import { sanitizationApi } from '../services/api';
import toast from 'react-hot-toast';

interface RulesListProps {
  config: SanitizationConfig | null;
  onRefresh: () => void;
  onEditRule: (rule: SanitizationRule) => void;
  onCreateRule: () => void;
}

const RulesList: React.FC<RulesListProps> = ({ config, onRefresh, onEditRule, onCreateRule }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRules, setSelectedRules] = useState<Set<string>>(new Set());
  const [filterType, setFilterType] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);

  const filteredRules = config?.rules.filter(rule => {
    const matchesSearch = 
      rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === 'all' || rule.type === filterType;
    const matchesSeverity = filterSeverity === 'all' || rule.severity === filterSeverity;
    const matchesStatus = 
      filterStatus === 'all' || 
      (filterStatus === 'enabled' && rule.enabled) ||
      (filterStatus === 'disabled' && !rule.enabled);

    return matchesSearch && matchesType && matchesSeverity && matchesStatus;
  }) || [];

  const handleSelectRule = (ruleId: string) => {
    const newSelected = new Set(selectedRules);
    if (newSelected.has(ruleId)) {
      newSelected.delete(ruleId);
    } else {
      newSelected.add(ruleId);
    }
    setSelectedRules(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedRules.size === filteredRules.length) {
      setSelectedRules(new Set());
    } else {
      setSelectedRules(new Set(filteredRules.map(rule => rule.id)));
    }
  };

  const handleBatchOperation = async (operation: 'enable' | 'disable' | 'delete') => {
    if (selectedRules.size === 0) {
      toast.error('Please select rules first');
      return;
    }

    const confirmMessage = `Are you sure you want to ${operation} ${selectedRules.size} rule(s)?`;
    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      const result = await sanitizationApi.batchOperation(Array.from(selectedRules), operation);
      toast.success(`${operation} operation completed: ${result.successCount} succeeded, ${result.failedCount} failed`);
      setSelectedRules(new Set());
      onRefresh();
    } catch (error) {
      toast.error(`Batch ${operation} failed`);
    }
  };

  const handleToggleRule = async (ruleId: string, enabled: boolean) => {
    try {
      await sanitizationApi.toggleRule(ruleId, enabled);
      toast.success(`Rule ${enabled ? 'enabled' : 'disabled'} successfully`);
      onRefresh();
    } catch (error) {
      toast.error(`Failed to ${enabled ? 'enable' : 'disable'} rule`);
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    if (!window.confirm('Are you sure you want to delete this rule?')) {
      return;
    }

    try {
      await sanitizationApi.deleteRule(ruleId);
      toast.success('Rule deleted successfully');
      onRefresh();
    } catch (error) {
      toast.error('Failed to delete rule');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-900/50 text-red-400 border border-red-800';
      case 'HIGH': return 'bg-orange-900/50 text-orange-400 border border-orange-800';
      case 'MEDIUM': return 'bg-yellow-900/50 text-yellow-400 border border-yellow-800';
      case 'LOW': return 'bg-green-900/50 text-green-400 border border-green-800';
      default: return 'bg-gray-800 text-gray-400 border border-gray-700';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'FIELD_NAME': return 'bg-blue-900/50 text-blue-400 border border-blue-800';
      case 'PATTERN': return 'bg-purple-900/50 text-purple-400 border border-purple-800';
      case 'CONTENT_TYPE': return 'bg-indigo-900/50 text-indigo-400 border border-indigo-800';
      default: return 'bg-gray-800 text-gray-400 border border-gray-700';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-white">Sanitization Rules</h2>
          <p className="text-gray-400 text-sm mt-1">
            {filteredRules.length} of {config?.rules.length || 0} rules
            {selectedRules.size > 0 && ` • ${selectedRules.size} selected`}
          </p>
        </div>
        <button
          onClick={onCreateRule}
          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Rule
        </button>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search rules..."
              className="w-full pl-10 pr-4 py-2 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${
              showFilters ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            }`}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-900 border border-gray-800 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Type</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="FIELD_NAME">Field Name</option>
                <option value="PATTERN">Pattern</option>
                <option value="CONTENT_TYPE">Content Type</option>
                <option value="CUSTOM">Custom</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Severity</label>
              <select
                value={filterSeverity}
                onChange={(e) => setFilterSeverity(e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Severities</option>
                <option value="CRITICAL">Critical</option>
                <option value="HIGH">High</option>
                <option value="MEDIUM">Medium</option>
                <option value="LOW">Low</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Status</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="enabled">Enabled</option>
                <option value="disabled">Disabled</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Batch Actions */}
      {selectedRules.size > 0 && (
        <div className="flex items-center justify-between p-4 bg-blue-900/20 border border-blue-800 rounded-lg">
          <span className="text-blue-400 font-medium">
            {selectedRules.size} rule(s) selected
          </span>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleBatchOperation('enable')}
              className="inline-flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
            >
              <Power className="h-3 w-3 mr-1" />
              Enable
            </button>
            <button
              onClick={() => handleBatchOperation('disable')}
              className="inline-flex items-center px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm transition-colors"
            >
              <PowerOff className="h-3 w-3 mr-1" />
              Disable
            </button>
            <button
              onClick={() => handleBatchOperation('delete')}
              className="inline-flex items-center px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Delete
            </button>
          </div>
        </div>
      )}

      {/* Rules List */}
      <div className="space-y-4">
        {filteredRules.length === 0 ? (
          <div className="text-center py-16">
            <Search className="mx-auto h-16 w-16 text-gray-600" />
            <h3 className="mt-4 text-lg font-medium text-gray-300">No rules found</h3>
            <p className="mt-2 text-sm text-gray-500">
              {searchTerm || filterType !== 'all' || filterSeverity !== 'all' || filterStatus !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Create your first sanitization rule to get started'
              }
            </p>
          </div>
        ) : (
          <>
            {/* Select All */}
            <div className="flex items-center space-x-3 p-4 bg-gray-900 border border-gray-800 rounded-lg">
              <button
                onClick={handleSelectAll}
                className="text-gray-400 hover:text-white transition-colors"
              >
                {selectedRules.size === filteredRules.length ? (
                  <CheckSquare className="h-5 w-5" />
                ) : (
                  <Square className="h-5 w-5" />
                )}
              </button>
              <span className="text-gray-400 text-sm">
                Select all {filteredRules.length} rules
              </span>
            </div>

            {/* Rules */}
            {filteredRules.map((rule) => (
              <div
                key={rule.id}
                className={`bg-gray-900 border rounded-2xl p-6 transition-all duration-200 hover:shadow-lg ${
                  selectedRules.has(rule.id) 
                    ? 'border-blue-500 bg-blue-900/10' 
                    : 'border-gray-800 hover:border-gray-700'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <button
                      onClick={() => handleSelectRule(rule.id)}
                      className="mt-1 text-gray-400 hover:text-white transition-colors"
                    >
                      {selectedRules.has(rule.id) ? (
                        <CheckSquare className="h-5 w-5" />
                      ) : (
                        <Square className="h-5 w-5" />
                      )}
                    </button>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="text-lg font-semibold text-white">{rule.name}</h4>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                          {rule.severity}
                        </span>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>
                          {rule.type}
                        </span>
                      </div>
                      <p className="text-sm text-gray-300 mb-2">{rule.description}</p>
                      <div className="text-xs text-gray-500 mb-3">ID: {rule.id}</div>
                      
                      <div className="space-y-2">
                        {rule.fieldNames && rule.fieldNames.length > 0 && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-400">Fields:</span>
                            <span className="ml-2 text-gray-300">{rule.fieldNames.join(', ')}</span>
                          </div>
                        )}
                        {rule.pattern && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-400">Pattern:</span>
                            <code className="ml-2 text-xs bg-gray-800 text-blue-400 px-2 py-1 rounded-md border border-gray-700">{rule.pattern}</code>
                          </div>
                        )}
                        <div className="text-sm">
                          <span className="font-medium text-gray-400">Mask:</span>
                          <code className="ml-2 text-xs bg-gray-800 text-green-400 px-2 py-1 rounded-md border border-gray-700">{rule.maskValue}</code>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      {rule.enabled ? (
                        <>
                          <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                          <span className="text-sm font-medium text-green-400">Enabled</span>
                        </>
                      ) : (
                        <>
                          <div className="w-2 h-2 bg-gray-600 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-gray-500">Disabled</span>
                        </>
                      )}
                    </div>
                    
                    <div className="relative">
                      <button
                        onClick={() => setActionMenuOpen(actionMenuOpen === rule.id ? null : rule.id)}
                        className="p-2 hover:bg-gray-800 rounded-full transition-colors"
                      >
                        <MoreVertical className="h-4 w-4 text-gray-400" />
                      </button>
                      
                      {actionMenuOpen === rule.id && (
                        <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10">
                          <button
                            onClick={() => {
                              onEditRule(rule);
                              setActionMenuOpen(null);
                            }}
                            className="w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-700 flex items-center space-x-2"
                          >
                            <Edit className="h-4 w-4" />
                            <span>Edit</span>
                          </button>
                          <button
                            onClick={() => {
                              handleToggleRule(rule.id, !rule.enabled);
                              setActionMenuOpen(null);
                            }}
                            className="w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-700 flex items-center space-x-2"
                          >
                            {rule.enabled ? <PowerOff className="h-4 w-4" /> : <Power className="h-4 w-4" />}
                            <span>{rule.enabled ? 'Disable' : 'Enable'}</span>
                          </button>
                          <button
                            onClick={() => {
                              handleDeleteRule(rule.id);
                              setActionMenuOpen(null);
                            }}
                            className="w-full px-4 py-2 text-left text-red-400 hover:bg-gray-700 flex items-center space-x-2"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>Delete</span>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
};

export default RulesList;
