import React, { useState } from 'react';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Power,
  PowerOff,
  Filter,
  MoreVertical,
  CheckSquare,
  Square,
  Download,
  Upload
} from 'lucide-react';
import { SanitizationRule, SanitizationConfig } from '../types';
import { sanitizationApi } from '../services/api';
import toast from 'react-hot-toast';

interface RulesListProps {
  config: SanitizationConfig | null;
  onRefresh: () => void;
  onEditRule: (rule: SanitizationRule) => void;
  onCreateRule: () => void;
}

const RulesList: React.FC<RulesListProps> = ({ config, onRefresh, onEditRule, onCreateRule }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRules, setSelectedRules] = useState<Set<string>>(new Set());
  const [filterType, setFilterType] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);

  const filteredRules = config?.rules.filter(rule => {
    const matchesSearch = 
      rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === 'all' || rule.type === filterType;
    const matchesSeverity = filterSeverity === 'all' || rule.severity === filterSeverity;
    const matchesStatus = 
      filterStatus === 'all' || 
      (filterStatus === 'enabled' && rule.enabled) ||
      (filterStatus === 'disabled' && !rule.enabled);

    return matchesSearch && matchesType && matchesSeverity && matchesStatus;
  }) || [];

  const handleSelectRule = (ruleId: string) => {
    const newSelected = new Set(selectedRules);
    if (newSelected.has(ruleId)) {
      newSelected.delete(ruleId);
    } else {
      newSelected.add(ruleId);
    }
    setSelectedRules(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedRules.size === filteredRules.length) {
      setSelectedRules(new Set());
    } else {
      setSelectedRules(new Set(filteredRules.map(rule => rule.id)));
    }
  };

  const handleBatchOperation = async (operation: 'enable' | 'disable' | 'delete') => {
    if (selectedRules.size === 0) {
      toast.error('Please select rules first');
      return;
    }

    const confirmMessage = `Are you sure you want to ${operation} ${selectedRules.size} rule(s)?`;
    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      const result = await sanitizationApi.batchOperation(Array.from(selectedRules), operation);
      toast.success(`${operation} operation completed: ${result.successCount} succeeded, ${result.failedCount} failed`);
      setSelectedRules(new Set());
      onRefresh();
    } catch (error) {
      toast.error(`Batch ${operation} failed`);
    }
  };

  const handleToggleRule = async (ruleId: string, enabled: boolean) => {
    try {
      await sanitizationApi.toggleRule(ruleId, enabled);
      toast.success(`Rule ${enabled ? 'enabled' : 'disabled'} successfully`);
      onRefresh();
    } catch (error) {
      toast.error(`Failed to ${enabled ? 'enable' : 'disable'} rule`);
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    if (!window.confirm('Are you sure you want to delete this rule?')) {
      return;
    }

    try {
      await sanitizationApi.deleteRule(ruleId);
      toast.success('Rule deleted successfully');
      onRefresh();
    } catch (error) {
      toast.error('Failed to delete rule');
    }
  };

  const handleExportRules = async () => {
    try {
      const config = await sanitizationApi.exportRules();
      const dataStr = JSON.stringify(config, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `sanitization-rules-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success('Rules exported successfully');
    } catch (error) {
      toast.error('Failed to export rules');
    }
  };

  const handleImportRules = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const config = JSON.parse(e.target?.result as string);
        await sanitizationApi.importRules(config);
        toast.success('Rules imported successfully');
        onRefresh();
      } catch (error) {
        toast.error('Failed to import rules');
      }
    };
    reader.readAsText(file);
    // Reset the input
    event.target.value = '';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-700 border border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-700 border border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-700 border border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-700 border border-green-200';
      default: return 'bg-gray-100 text-gray-700 border border-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'FIELD_NAME': return 'bg-blue-100 text-blue-700 border border-blue-200';
      case 'PATTERN': return 'bg-purple-100 text-purple-700 border border-purple-200';
      case 'CONTENT_TYPE': return 'bg-indigo-100 text-indigo-700 border border-indigo-200';
      default: return 'bg-gray-100 text-gray-700 border border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">脱敏规则管理</h2>
            <p className="text-gray-600 text-sm mt-1">
              共 {config?.rules.length || 0} 条规则，显示 {filteredRules.length} 条
              {selectedRules.size > 0 && ` • 已选择 ${selectedRules.size} 条`}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleExportRules}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              导出配置
            </button>
            <label className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors cursor-pointer">
              <Upload className="h-4 w-4 mr-2" />
              导入配置
              <input
                type="file"
                accept=".json"
                onChange={handleImportRules}
                className="hidden"
              />
            </label>
            <button
              onClick={onCreateRule}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              新建规则
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索规则名称、描述或ID..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              showFilters
                ? 'bg-blue-600 text-white'
                : 'border border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
            }`}
          >
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-200">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">规则类型</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部类型</option>
                <option value="FIELD_NAME">字段名匹配</option>
                <option value="PATTERN">正则匹配</option>
                <option value="CONTENT_TYPE">内容类型</option>
                <option value="CUSTOM">自定义</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">严重程度</label>
              <select
                value={filterSeverity}
                onChange={(e) => setFilterSeverity(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部级别</option>
                <option value="CRITICAL">关键</option>
                <option value="HIGH">高</option>
                <option value="MEDIUM">中等</option>
                <option value="LOW">低</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">启用状态</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">全部状态</option>
                <option value="enabled">已启用</option>
                <option value="disabled">已禁用</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Batch Actions */}
      {selectedRules.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-blue-700 font-medium">
              已选择 {selectedRules.size} 条规则
            </span>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBatchOperation('enable')}
                className="inline-flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
              >
                <Power className="h-3 w-3 mr-1" />
                批量启用
              </button>
              <button
                onClick={() => handleBatchOperation('disable')}
                className="inline-flex items-center px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-sm transition-colors"
              >
                <PowerOff className="h-3 w-3 mr-1" />
                批量禁用
              </button>
              <button
                onClick={() => handleBatchOperation('delete')}
                className="inline-flex items-center px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                批量删除
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Rules List */}
      <div className="space-y-4">
        {filteredRules.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-16 text-center">
            <Search className="mx-auto h-16 w-16 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">未找到规则</h3>
            <p className="mt-2 text-sm text-gray-500">
              {searchTerm || filterType !== 'all' || filterSeverity !== 'all' || filterStatus !== 'all'
                ? '请尝试调整搜索条件或筛选器'
                : '创建您的第一个脱敏规则开始使用'
              }
            </p>
          </div>
        ) : (
          <>
            {/* Select All */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleSelectAll}
                  className="text-gray-500 hover:text-gray-700 transition-colors"
                >
                  {selectedRules.size === filteredRules.length ? (
                    <CheckSquare className="h-5 w-5" />
                  ) : (
                    <Square className="h-5 w-5" />
                  )}
                </button>
                <span className="text-gray-600 text-sm">
                  全选 {filteredRules.length} 条规则
                </span>
              </div>
            </div>

            {/* Rules */}
            {filteredRules.map((rule) => (
              <div
                key={rule.id}
                className={`bg-white border rounded-lg p-6 transition-all duration-200 hover:shadow-md ${
                  selectedRules.has(rule.id)
                    ? 'border-blue-300 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <button
                      onClick={() => handleSelectRule(rule.id)}
                      className="mt-1 text-gray-500 hover:text-gray-700 transition-colors"
                    >
                      {selectedRules.has(rule.id) ? (
                        <CheckSquare className="h-5 w-5" />
                      ) : (
                        <Square className="h-5 w-5" />
                      )}
                    </button>

                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="text-lg font-semibold text-gray-900">{rule.name}</h4>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                          {rule.severity === 'CRITICAL' ? '关键' :
                           rule.severity === 'HIGH' ? '高' :
                           rule.severity === 'MEDIUM' ? '中等' : '低'}
                        </span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(rule.type)}`}>
                          {rule.type === 'FIELD_NAME' ? '字段匹配' :
                           rule.type === 'PATTERN' ? '正则匹配' :
                           rule.type === 'CONTENT_TYPE' ? '内容类型' : '自定义'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{rule.description}</p>
                      <div className="text-xs text-gray-500 mb-3">规则ID: {rule.id}</div>
                      
                      <div className="space-y-2">
                        {rule.fieldNames && rule.fieldNames.length > 0 && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-700">匹配字段:</span>
                            <span className="ml-2 text-gray-600">{rule.fieldNames.join(', ')}</span>
                          </div>
                        )}
                        {rule.pattern && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-700">正则表达式:</span>
                            <code className="ml-2 text-xs bg-gray-100 text-blue-600 px-2 py-1 rounded border border-gray-200">{rule.pattern}</code>
                          </div>
                        )}
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">脱敏值:</span>
                          <code className="ml-2 text-xs bg-gray-100 text-green-600 px-2 py-1 rounded border border-gray-200">{rule.maskValue}</code>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      {rule.enabled ? (
                        <>
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-green-600">已启用</span>
                        </>
                      ) : (
                        <>
                          <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                          <span className="text-sm font-medium text-gray-500">已禁用</span>
                        </>
                      )}
                    </div>

                    <div className="relative">
                      <button
                        onClick={() => setActionMenuOpen(actionMenuOpen === rule.id ? null : rule.id)}
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                      >
                        <MoreVertical className="h-4 w-4 text-gray-500" />
                      </button>

                      {actionMenuOpen === rule.id && (
                        <div className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                          <button
                            onClick={() => {
                              onEditRule(rule);
                              setActionMenuOpen(null);
                            }}
                            className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2 text-sm"
                          >
                            <Edit className="h-4 w-4" />
                            <span>编辑</span>
                          </button>
                          <button
                            onClick={() => {
                              handleToggleRule(rule.id, !rule.enabled);
                              setActionMenuOpen(null);
                            }}
                            className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2 text-sm"
                          >
                            {rule.enabled ? <PowerOff className="h-4 w-4" /> : <Power className="h-4 w-4" />}
                            <span>{rule.enabled ? '禁用' : '启用'}</span>
                          </button>
                          <button
                            onClick={() => {
                              handleDeleteRule(rule.id);
                              setActionMenuOpen(null);
                            }}
                            className="w-full px-4 py-2 text-left text-red-600 hover:bg-gray-50 flex items-center space-x-2 text-sm"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>删除</span>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
};

export default RulesList;
