{"version": "1.0.0", "timestamp": 1752804392620, "enabled": false, "markersEnabled": false, "markerFormat": "BRACKET", "rules": [{"id": "password-fields", "name": "Password Fields", "description": "Sanitize common password field names - completely masked", "type": "FIELD_NAME", "severity": "HIGH", "enabled": true, "priority": 100, "fieldNames": ["password", "passwd", "pwd", "secret", "token", "<PERSON><PERSON><PERSON><PERSON>", "api_key", "accessToken", "access_token", "refreshToken", "refresh_token"], "maskValue": "********", "markerType": "PASSWORD", "preserveFormat": false, "preserveLength": 0}, {"id": "email-pattern", "name": "Email Addresses", "description": "Sanitize email addresses - show first 2 chars and domain", "type": "PATTERN", "severity": "MEDIUM", "enabled": true, "priority": 200, "pattern": "([a-zA-Z0-9._%+-]{1,2})[a-zA-Z0-9._%+-]*@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})", "contentTypes": ["application/json", "application/xml", "text/plain"], "maskValue": "$1***@$2", "markerType": "EMAIL", "preserveFormat": true, "preserveLength": 0}, {"id": "credit-card", "name": "Credit Card Numbers", "description": "Sanitize credit card numbers - show last 4 digits only", "type": "PATTERN", "severity": "CRITICAL", "enabled": true, "priority": 50, "pattern": "\\b(\\d{4})[-\\s]?(\\d{4})[-\\s]?(\\d{4})[-\\s]?(\\d{4})\\b", "maskValue": "****-****-****-$4", "markerType": "CREDIT_CARD", "preserveFormat": false, "preserveLength": 0}, {"id": "phone-numbers", "name": "Phone Numbers", "description": "Sanitize phone numbers - show last 4 digits only", "type": "PATTERN", "severity": "MEDIUM", "enabled": true, "priority": 150, "pattern": "(\\+?1?[-\\.\\s]?\\(?[0-9]{3}\\)?[-\\.\\s]?[0-9]{3})[-\\.\\s]?([0-9]{4})", "maskValue": "***-***-$2", "markerType": "PHONE", "preserveFormat": false, "preserveLength": 0}, {"id": "username-fields", "name": "Username Fields", "description": "Sanitize username fields - show first 2 and last 1 characters", "type": "FIELD_NAME", "severity": "MEDIUM", "enabled": true, "priority": 180, "fieldNames": ["username", "user_name", "userName", "loginName", "login_name"], "maskValue": "PARTIAL_MASK", "markerType": "USERNAME", "preserveFormat": false, "preserveLength": 0}, {"id": "ssn", "name": "Social Security Numbers", "description": "Sanitize US Social Security Numbers", "type": "PATTERN", "severity": "CRITICAL", "enabled": true, "priority": 25, "pattern": "\\b\\d{3}-?\\d{2}-?\\d{4}\\b", "maskValue": "***-**-****", "markerType": "SSN", "preserveFormat": false, "preserveLength": 0}, {"id": "api-keys", "name": "API Keys", "description": "Sanitize API keys and tokens", "type": "PATTERN", "severity": "HIGH", "enabled": true, "priority": 75, "pattern": "(?i)(api[_-]?key|token|secret)[\"\\s]*[:=][\"\\s]*[a-zA-Z0-9_-]{16,}", "maskValue": "****", "markerType": "API_KEY", "preserveFormat": false, "preserveLength": 0}, {"id": "db-connection", "name": "Database Connection Strings", "description": "Sanitize database connection strings", "type": "PATTERN", "severity": "HIGH", "enabled": true, "priority": 80, "pattern": "(?i)(password|pwd)[\"\\s]*[:=][\"\\s]*[^\"\\s;]+", "maskValue": "****", "markerType": "DB_PASSWORD", "preserveFormat": false, "preserveLength": 0}, {"id": "payment-account-number", "name": "Payment Account Numbers", "description": "Sanitize account numbers for payment service", "type": "FIELD_NAME", "severity": "HIGH", "enabled": true, "priority": 60, "fieldNames": ["accountNumber", "account_number", "routingNumber", "routing_number", "cvv", "cvc", "securityCode", "security_code"], "maskValue": "***", "markerType": "PAYMENT_DATA", "preserveFormat": false, "preserveLength": 0, "includeServices": ["payment-service", "billing-service"]}, {"id": "user-pii", "name": "User Personal Information", "description": "Sanitize PII for user service", "type": "FIELD_NAME", "severity": "MEDIUM", "enabled": true, "priority": 120, "fieldNames": ["firstName", "first_name", "lastName", "last_name", "fullName", "full_name", "dateOfBirth", "date_of_birth", "dob"], "maskValue": "***", "markerType": "PII", "preserveFormat": false, "preserveLength": 0, "includeServices": ["user-service", "profile-service"]}], "globalSettings": {"defaultMaskValue": "****", "enableLogging": true, "enableMetrics": true, "logLevel": "INFO", "maxRulesPriority": 1000}}