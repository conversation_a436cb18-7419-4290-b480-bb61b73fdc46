# 📚 文档索引

## 🚀 快速开始

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [简洁UI使用指南](./SIMPLE_UI_GUIDE.md) | 新版简洁界面使用说明 | 所有用户 |
| [快速配置指南](./QUICK_CONFIG.md) | 5分钟快速上手，常用规则模板 | 新用户、快速部署 |
| [启动脚本说明](./start-dev.sh) | 开发环境一键启动 | 开发人员 |

## 📖 详细文档

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [配置说明文档](./CONFIGURATION_GUIDE.md) | 完整的配置参数和规则说明 | 系统管理员、高级用户 |
| [功能特性说明](./ENHANCED_FEATURES.md) | 新功能介绍和使用指南 | 产品经理、用户 |
| [英文文档](./README_EN.md) | English documentation | International users |

## 🔧 技术文档

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [API接口文档](./README_EN.md#api-endpoints) | RESTful API详细说明 | 开发人员、集成商 |
| [前端开发文档](./FRONTEND_README.md) | React前端开发指南 | 前端开发人员 |
| [项目总结](./PROJECT_SUMMARY.md) | 项目架构和技术总结 | 架构师、技术负责人 |

## 📋 配置文件

| 文件 | 描述 | 用途 |
|------|------|------|
| [规则配置模板](./config/rules.template.json) | 完整的规则配置示例 | 配置参考、快速部署 |
| [当前规则配置](./config/rules.json) | 当前生效的规则配置 | 系统运行配置 |
| [YAML配置示例](./config/rules.yaml) | YAML格式配置示例 | 配置参考 |

## 🧪 测试文档

| 文件 | 描述 | 用途 |
|------|------|------|
| [后端API测试](./test_service.sh) | 后端接口测试脚本 | API测试、功能验证 |
| [前端功能测试](./test-frontend.sh) | 前端功能测试指南 | UI测试、用户体验验证 |
| [集成测试文档](./integration_test.md) | 集成测试说明 | 系统集成测试 |

## 🐳 部署文档

| 文件 | 描述 | 用途 |
|------|------|------|
| [Docker配置](./docker-compose.yml) | Docker容器编排配置 | 容器化部署 |
| [生产启动脚本](./start.sh) | 生产环境启动脚本 | 生产部署 |
| [Makefile](./Makefile) | 构建和部署自动化 | 自动化部署 |

## 📊 使用场景

### 🆕 新用户入门
1. [简洁UI使用指南](./SIMPLE_UI_GUIDE.md) - 界面操作说明
2. [快速配置指南](./QUICK_CONFIG.md) - 了解基本概念
3. [启动脚本](./start-dev.sh) - 快速启动服务
4. [规则模板](./config/rules.template.json) - 参考配置示例

### 🔧 系统配置
1. [配置说明文档](./CONFIGURATION_GUIDE.md) - 详细配置参数
2. [API接口文档](./README_EN.md#api-endpoints) - 接口调用
3. [测试验证](./test_service.sh) - 功能测试

### 🚀 生产部署
1. [Docker部署](./docker-compose.yml) - 容器化部署
2. [生产启动](./start.sh) - 生产环境配置
3. [监控指标](./README_EN.md#metrics) - 系统监控

### 🛠️ 开发定制
1. [前端开发](./FRONTEND_README.md) - 界面定制
2. [API扩展](./handlers/) - 后端扩展
3. [项目架构](./PROJECT_SUMMARY.md) - 技术架构

## 🔍 快速查找

### 按功能查找
- **规则配置**: [配置说明文档](./CONFIGURATION_GUIDE.md) → 规则配置详解
- **API调用**: [英文文档](./README_EN.md) → API Endpoints
- **界面操作**: [快速配置指南](./QUICK_CONFIG.md) → Web界面操作
- **故障排除**: [配置说明文档](./CONFIGURATION_GUIDE.md) → 故障排除

### 按角色查找
- **系统管理员**: 配置文档 → 部署文档 → 测试文档
- **开发人员**: API文档 → 技术文档 → 测试脚本
- **最终用户**: 快速指南 → 功能说明 → 界面操作
- **运维人员**: 部署文档 → 监控指标 → 故障排除

## 📝 文档更新记录

| 版本 | 日期 | 更新内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2024-01-XX | 初始版本，基础功能文档 | System |
| v1.1 | 2024-01-XX | 增加配置说明和快速指南 | System |
| v1.2 | 2024-01-XX | 完善API文档和测试指南 | System |

## 🤝 贡献指南

### 文档贡献
1. 发现文档问题或改进建议
2. 提交Issue或Pull Request
3. 遵循文档格式规范
4. 更新相关索引

### 格式规范
- 使用Markdown格式
- 保持结构清晰
- 添加适当的emoji图标
- 包含代码示例和截图

## 📞 获取帮助

- **文档问题**: 查看对应文档的故障排除章节
- **功能疑问**: 参考[功能特性说明](./ENHANCED_FEATURES.md)
- **配置问题**: 查看[配置说明文档](./CONFIGURATION_GUIDE.md)
- **API问题**: 参考[API接口文档](./README_EN.md#api-endpoints)

---

💡 **提示**: 建议按照使用场景选择对应的文档，可以更高效地找到所需信息。
