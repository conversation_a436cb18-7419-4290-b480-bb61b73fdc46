package service

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"regexp"
	"sync"
	"time"

	"sanitization-config-service/models"
)

// RuleService manages sanitization rules
type RuleService struct {
	config       *models.SanitizationConfig
	configFile   string
	mutex        sync.RWMutex
	lastModified time.Time
}

// NewRuleService creates a new rule service
func NewRuleService(configFile string) *RuleService {
	service := &RuleService{
		configFile: configFile,
		config:     models.NewSanitizationConfig(),
	}

	// Load initial configuration
	if err := service.LoadConfig(); err != nil {
		log.Printf("Failed to load initial config: %v", err)
		// Use default configuration with some basic rules
		service.loadDefaultRules()
	}

	return service
}

// LoadConfig loads configuration from file
func (s *RuleService) LoadConfig() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Check if file exists
	if _, err := os.Stat(s.configFile); os.IsNotExist(err) {
		log.Printf("Config file %s does not exist, using default rules", s.configFile)
		s.loadDefaultRules()
		return nil
	}

	// Get file modification time
	fileInfo, err := os.Stat(s.configFile)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	// Skip if file hasn't been modified
	if !fileInfo.ModTime().After(s.lastModified) {
		return nil
	}

	// Read file
	data, err := os.ReadFile(s.configFile)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// Parse JSON (simplified for now)
	var config models.SanitizationConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to parse config file: %w", err)
	}

	// Validate configuration
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid configuration: %w", err)
	}

	s.config = &config
	s.lastModified = fileInfo.ModTime()

	log.Printf("Loaded configuration with %d rules", len(config.Rules))
	return nil
}

// GetConfig returns the current sanitization configuration
func (s *RuleService) GetConfig() *models.SanitizationConfig {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Create a copy to avoid race conditions
	configCopy := *s.config
	configCopy.Rules = make([]models.SanitizationRule, len(s.config.Rules))
	copy(configCopy.Rules, s.config.Rules)

	return &configCopy
}

// GetConfigForService returns configuration filtered for a specific service
func (s *RuleService) GetConfigForService(serviceName string) *models.SanitizationConfig {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	config := *s.config
	config.Rules = s.config.GetRulesForService(serviceName)

	return &config
}

// ReloadConfig reloads configuration from file
func (s *RuleService) ReloadConfig() error {
	return s.LoadConfig()
}

// SetGlobalEnabled sets the global sanitization enabled flag
func (s *RuleService) SetGlobalEnabled(enabled bool) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.config.Enabled = enabled
	s.config.Timestamp = time.Now().UnixMilli()

	// Save the updated configuration to file
	return s.saveConfig()
}

// saveConfig saves the current configuration to file
func (s *RuleService) saveConfig() error {
	data, err := json.MarshalIndent(s.config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(s.configFile, data, 0o644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	log.Printf("Configuration saved to %s", s.configFile)
	return nil
}

// loadDefaultRules loads default sanitization rules
func (s *RuleService) loadDefaultRules() {
	s.config = models.NewSanitizationConfig()

	// Password rule
	passwordRule := models.SanitizationRule{
		ID:          "default-password",
		Name:        "Password Fields",
		Description: "Sanitize common password field names",
		Type:        models.FieldNameRule,
		Severity:    models.HighSeverity,
		Enabled:     true,
		Priority:    100,
		FieldNames:  []string{"password", "passwd", "pwd", "secret", "token", "apiKey", "api_key"},
		MaskValue:   "****",
		MarkerType:  "PASSWORD",
	}

	// Email pattern rule
	emailRule := models.SanitizationRule{
		ID:             "default-email",
		Name:           "Email Addresses",
		Description:    "Sanitize email addresses using pattern matching",
		Type:           models.PatternRule,
		Severity:       models.MediumSeverity,
		Enabled:        true,
		Priority:       200,
		Pattern:        `[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`,
		ContentTypes:   []string{"application/json", "application/xml", "text/plain"},
		MaskValue:      "****@****.***",
		MarkerType:     "EMAIL",
		PreserveFormat: true,
	}

	// Credit card rule
	creditCardRule := models.SanitizationRule{
		ID:          "default-credit-card",
		Name:        "Credit Card Numbers",
		Description: "Sanitize credit card numbers",
		Type:        models.PatternRule,
		Severity:    models.CriticalSeverity,
		Enabled:     true,
		Priority:    50,
		Pattern:     `\b(?:\d{4}[-\s]?){3}\d{4}\b`,
		MaskValue:   "****-****-****-****",
		MarkerType:  "CREDIT_CARD",
	}

	// Phone number rule
	phoneRule := models.SanitizationRule{
		ID:          "default-phone",
		Name:        "Phone Numbers",
		Description: "Sanitize phone numbers",
		Type:        models.PatternRule,
		Severity:    models.MediumSeverity,
		Enabled:     true,
		Priority:    150,
		Pattern:     `\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}`,
		MaskValue:   "***-***-****",
		MarkerType:  "PHONE",
	}

	// SSN rule
	ssnRule := models.SanitizationRule{
		ID:          "default-ssn",
		Name:        "Social Security Numbers",
		Description: "Sanitize US Social Security Numbers",
		Type:        models.PatternRule,
		Severity:    models.CriticalSeverity,
		Enabled:     true,
		Priority:    25,
		Pattern:     `\b\d{3}-?\d{2}-?\d{4}\b`,
		MaskValue:   "***-**-****",
		MarkerType:  "SSN",
	}

	s.config.AddRule(passwordRule)
	s.config.AddRule(emailRule)
	s.config.AddRule(creditCardRule)
	s.config.AddRule(phoneRule)
	s.config.AddRule(ssnRule)

	// Set global settings
	s.config.GlobalSettings = map[string]interface{}{
		"defaultMaskValue": "****",
		"enableLogging":    true,
		"logLevel":         "INFO",
	}

	log.Printf("Loaded %d default rules", len(s.config.Rules))
}

// CreateRule creates a new sanitization rule
func (s *RuleService) CreateRule(rule models.SanitizationRule) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Check if rule with same ID already exists
	for _, existingRule := range s.config.Rules {
		if existingRule.ID == rule.ID {
			return fmt.Errorf("rule with ID %s already exists", rule.ID)
		}
	}

	// Validate rule
	if err := s.validateRule(rule); err != nil {
		return fmt.Errorf("invalid rule: %w", err)
	}

	// Add rule to configuration
	s.config.Rules = append(s.config.Rules, rule)
	s.config.Timestamp = time.Now().UnixMilli()

	// Save configuration
	return s.saveConfig()
}

// UpdateRule updates an existing sanitization rule
func (s *RuleService) UpdateRule(rule models.SanitizationRule) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Find rule index
	ruleIndex := -1
	for i, existingRule := range s.config.Rules {
		if existingRule.ID == rule.ID {
			ruleIndex = i
			break
		}
	}

	if ruleIndex == -1 {
		return fmt.Errorf("rule not found")
	}

	// Validate rule
	if err := s.validateRule(rule); err != nil {
		return fmt.Errorf("invalid rule: %w", err)
	}

	// Update rule
	s.config.Rules[ruleIndex] = rule
	s.config.Timestamp = time.Now().UnixMilli()

	// Save configuration
	return s.saveConfig()
}

// DeleteRule deletes a sanitization rule
func (s *RuleService) DeleteRule(ruleID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Find rule index
	ruleIndex := -1
	for i, rule := range s.config.Rules {
		if rule.ID == ruleID {
			ruleIndex = i
			break
		}
	}

	if ruleIndex == -1 {
		return fmt.Errorf("rule not found")
	}

	// Remove rule from slice
	s.config.Rules = append(s.config.Rules[:ruleIndex], s.config.Rules[ruleIndex+1:]...)
	s.config.Timestamp = time.Now().UnixMilli()

	// Save configuration
	return s.saveConfig()
}

// ToggleRule toggles a rule's enabled status
func (s *RuleService) ToggleRule(ruleID string, enabled bool) (*models.SanitizationRule, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Find rule
	for i, rule := range s.config.Rules {
		if rule.ID == ruleID {
			s.config.Rules[i].Enabled = enabled
			s.config.Timestamp = time.Now().UnixMilli()

			// Save configuration
			if err := s.saveConfig(); err != nil {
				return nil, err
			}

			return &s.config.Rules[i], nil
		}
	}

	return nil, fmt.Errorf("rule not found")
}

// BatchOperationResult represents the result of a batch operation
type BatchOperationResult struct {
	SuccessCount int
	FailedCount  int
	FailedRules  []string
}

// BatchOperation performs batch operations on multiple rules
func (s *RuleService) BatchOperation(ruleIDs []string, operation string) (*BatchOperationResult, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	result := &BatchOperationResult{
		FailedRules: make([]string, 0),
	}

	for _, ruleID := range ruleIDs {
		var err error
		switch operation {
		case "enable":
			err = s.toggleRuleInternal(ruleID, true)
		case "disable":
			err = s.toggleRuleInternal(ruleID, false)
		case "delete":
			err = s.deleteRuleInternal(ruleID)
		default:
			err = fmt.Errorf("unknown operation: %s", operation)
		}

		if err != nil {
			result.FailedCount++
			result.FailedRules = append(result.FailedRules, ruleID)
		} else {
			result.SuccessCount++
		}
	}

	// Save configuration if any operations succeeded
	if result.SuccessCount > 0 {
		s.config.Timestamp = time.Now().UnixMilli()
		if err := s.saveConfig(); err != nil {
			return nil, fmt.Errorf("failed to save configuration: %w", err)
		}
	}

	return result, nil
}

// ValidationResult represents the result of rule validation
type ValidationResult struct {
	Valid      bool
	Errors     []string
	TestOutput string
}

// ValidateRule validates a rule configuration and optionally tests it
func (s *RuleService) ValidateRule(rule models.SanitizationRule, testInput string) *ValidationResult {
	result := &ValidationResult{
		Valid:  true,
		Errors: make([]string, 0),
	}

	// Validate rule structure
	if err := s.validateRule(rule); err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, err.Error())
	}

	// Test rule if input provided
	if testInput != "" && result.Valid {
		output, err := s.testRule(rule, testInput)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Test failed: %v", err))
		} else {
			result.TestOutput = output
		}
	}

	return result
}

// validateRule validates a single rule
func (s *RuleService) validateRule(rule models.SanitizationRule) error {
	if rule.ID == "" {
		return fmt.Errorf("rule ID is required")
	}

	if rule.Name == "" {
		return fmt.Errorf("rule name is required")
	}

	if rule.Type == "" {
		return fmt.Errorf("rule type is required")
	}

	if rule.MaskValue == "" {
		return fmt.Errorf("mask value is required")
	}

	// Validate rule type specific fields
	switch rule.Type {
	case models.FieldNameRule:
		if len(rule.FieldNames) == 0 {
			return fmt.Errorf("field names are required for FIELD_NAME rule type")
		}
	case models.PatternRule:
		if rule.Pattern == "" {
			return fmt.Errorf("pattern is required for PATTERN rule type")
		}
		// Validate regex pattern
		if _, err := regexp.Compile(rule.Pattern); err != nil {
			return fmt.Errorf("invalid regex pattern: %w", err)
		}
	case models.ContentTypeRule:
		if len(rule.ContentTypes) == 0 {
			return fmt.Errorf("content types are required for CONTENT_TYPE rule type")
		}
	}

	return nil
}

// testRule tests a rule against sample input
func (s *RuleService) testRule(rule models.SanitizationRule, input string) (string, error) {
	switch rule.Type {
	case models.PatternRule:
		if rule.Pattern == "" {
			return input, nil
		}

		regex, err := regexp.Compile(rule.Pattern)
		if err != nil {
			return "", fmt.Errorf("invalid regex pattern: %w", err)
		}

		return regex.ReplaceAllString(input, rule.MaskValue), nil

	case models.FieldNameRule:
		// For field name rules, we simulate JSON field replacement
		for _, fieldName := range rule.FieldNames {
			// Simple field replacement for testing
			pattern := fmt.Sprintf(`"%s"\s*:\s*"[^"]*"`, fieldName)
			regex, err := regexp.Compile(pattern)
			if err != nil {
				continue
			}
			replacement := fmt.Sprintf(`"%s": "%s"`, fieldName, rule.MaskValue)
			input = regex.ReplaceAllString(input, replacement)
		}
		return input, nil

	default:
		return input, nil
	}
}

// toggleRuleInternal toggles a rule's enabled status (internal method, no locking)
func (s *RuleService) toggleRuleInternal(ruleID string, enabled bool) error {
	for i, rule := range s.config.Rules {
		if rule.ID == ruleID {
			s.config.Rules[i].Enabled = enabled
			return nil
		}
	}
	return fmt.Errorf("rule not found")
}

// deleteRuleInternal deletes a rule (internal method, no locking)
func (s *RuleService) deleteRuleInternal(ruleID string) error {
	for i, rule := range s.config.Rules {
		if rule.ID == ruleID {
			s.config.Rules = append(s.config.Rules[:i], s.config.Rules[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("rule not found")
}
